{"rustc": 1842507548689473721, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 11876527447619405325, "path": 6767152828495364124, "deps": [[7312356825837975969, "crc32fast", false, 1491725788949021594], [7636735136738807108, "miniz_oxide", false, 4692828213084011560]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-e0cd83e7056026ef\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}