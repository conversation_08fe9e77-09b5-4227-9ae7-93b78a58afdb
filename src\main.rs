use anyhow::Result;
use clap::{Args, Parser, Subcommand};
use colored::*;
use std::env;

mod cache;
mod cli;
mod commands;
mod errors;
mod extractor;
mod installer;
mod resolver;
mod types;
mod ui;
mod utils;

use crate::commands::*;
use crate::errors::NxError;
use crate::ui::show_banner;

#[derive(Parser)]
#[command(
    name = "nx",
    version = "0.2.0",
    about = "⚡ Ultra-fast package manager for Node.js ecosystems",
    long_about = "NX is a blazingly fast package manager built in Rust that outperforms npm, pnpm, yarn, and bun with intelligent caching, parallel downloads, and beautiful progress indicators."
)]
struct Cli {
    #[command(subcommand)]
    command: Commands,
    
    /// Enable verbose output
    #[arg(short, long, global = true)]
    verbose: bool,
    
    /// Disable colored output
    #[arg(long, global = true)]
    no_color: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// Install packages
    #[command(aliases = ["i", "add"])]
    Install(InstallCommand),
    
    /// Remove packages
    #[command(aliases = ["rm", "remove", "uninstall"])]
    Remove(RemoveCommand),
    
    /// Update packages
    #[command(aliases = ["up", "upgrade"])]
    Update(UpdateCommand),
    
    /// List installed packages
    #[command(aliases = ["ls"])]
    List(ListCommand),
    
    /// Show package information
    #[command(aliases = ["info", "view"])]
    Show(ShowCommand),
    
    /// Search for packages
    #[command(aliases = ["find"])]
    Search(SearchCommand),
    
    /// Run package scripts
    Run(RunCommand),
    
    /// Initialize a new project
    Init(InitCommand),
    
    /// Clean cache and node_modules
    Clean(CleanCommand),
    
    /// Show NX configuration
    Config(ConfigCommand),
    
    /// Publish package to registry
    Publish(PublishCommand),
    
    /// Login to registry
    Login(LoginCommand),
    
    /// Logout from registry
    Logout(LogoutCommand),
    
    /// Audit packages for vulnerabilities
    Audit(AuditCommand),
    
    /// Link/unlink packages for development
    Link(LinkCommand),
    
    /// Create package from template
    Create(CreateCommand),
    
    /// Execute package binaries
    Exec(ExecCommand),
    
    /// Show dependency tree
    Tree(TreeCommand),
    
    /// Outdated packages check
    Outdated(OutdatedCommand),
    
    /// Run tests
    Test(TestCommand),
    
    /// Start development server
    Start(StartCommand),
    
    /// Build project
    Build(BuildCommand),
}

#[derive(Args)]
struct InstallCommand {
    /// Package names to install
    packages: Vec<String>,
    
    /// Save as dev dependency
    #[arg(short = 'D', long)]
    save_dev: bool,
    
    /// Save as optional dependency
    #[arg(short = 'O', long)]
    save_optional: bool,
    
    /// Save exact version
    #[arg(short = 'E', long)]
    save_exact: bool,
    
    /// Install globally
    #[arg(short, long)]
    global: bool,
    
    /// Perform dry run
    #[arg(long)]
    dry_run: bool,
    
    /// Force reinstall
    #[arg(short, long)]
    force: bool,
    
    /// Skip running lifecycle scripts
    #[arg(long)]
    ignore_scripts: bool,
    
    /// Production install (skip dev dependencies)
    #[arg(long)]
    production: bool,
}

#[derive(Args)]
struct RemoveCommand {
    /// Package names to remove
    packages: Vec<String>,
    
    /// Remove globally
    #[arg(short, long)]
    global: bool,
}

#[derive(Args)]
struct UpdateCommand {
    /// Package names to update (empty = all)
    packages: Vec<String>,
    
    /// Update globally
    #[arg(short, long)]
    global: bool,
    
    /// Check for updates only
    #[arg(long)]
    check: bool,
}

#[derive(Args)]
struct ListCommand {
    /// List global packages
    #[arg(short, long)]
    global: bool,
    
    /// Show dependency tree
    #[arg(long)]
    tree: bool,
    
    /// Show outdated packages
    #[arg(long)]
    outdated: bool,
}

#[derive(Args)]
struct ShowCommand {
    /// Package name
    package: String,
    
    /// Show specific version
    #[arg(short, long)]
    version: Option<String>,
}

#[derive(Args)]
struct SearchCommand {
    /// Search query
    query: String,
    
    /// Limit results
    #[arg(short, long, default_value = "20")]
    limit: usize,
}

#[derive(Args)]
struct RunCommand {
    /// Script name
    script: String,
    
    /// Script arguments
    args: Vec<String>,
}

#[derive(Args)]
struct InitCommand {
    /// Project name
    name: Option<String>,
    
    /// Use TypeScript template
    #[arg(long)]
    typescript: bool,
    
    /// Skip interactive prompts
    #[arg(short, long)]
    yes: bool,
}

#[derive(Args)]
struct CleanCommand {
    /// Clean cache only
    #[arg(long)]
    cache: bool,
    
    /// Clean node_modules only
    #[arg(long)]
    modules: bool,
    
    /// Clean everything
    #[arg(short, long)]
    all: bool,
}

#[derive(Args)]
struct ConfigCommand {
    /// Configuration key
    key: Option<String>,
    
    /// Configuration value
    value: Option<String>,
    
    /// List all configuration
    #[arg(short, long)]
    list: bool,
}

#[derive(Args)]
struct PublishCommand {
    /// Package directory
    path: Option<String>,
    
    /// Tag for release
    #[arg(long)]
    tag: Option<String>,
    
    /// Dry run
    #[arg(long)]
    dry_run: bool,
}

#[derive(Args)]
struct LoginCommand {
    /// Registry URL
    registry: Option<String>,
}

#[derive(Args)]
struct LogoutCommand {
    /// Registry URL
    registry: Option<String>,
}

#[derive(Args)]
struct AuditCommand {
    /// Fix vulnerabilities automatically
    #[arg(long)]
    fix: bool,
    
    /// Show detailed audit report
    #[arg(long)]
    detailed: bool,
}

#[derive(Args)]
struct LinkCommand {
    /// Package path to link
    package: Option<String>,
    
    /// Unlink package
    #[arg(long)]
    unlink: bool,
}

#[derive(Args)]
struct CreateCommand {
    /// Template name
    template: String,
    
    /// Project name
    name: String,
}

#[derive(Args)]
struct ExecCommand {
    /// Command to execute
    command: String,
    
    /// Command arguments
    args: Vec<String>,
}

#[derive(Args)]
struct TreeCommand {
    /// Show only production dependencies
    #[arg(long)]
    production: bool,
    
    /// Maximum depth
    #[arg(short, long)]
    depth: Option<u32>,
}

#[derive(Args)]
struct OutdatedCommand {
    /// Show global packages
    #[arg(short, long)]
    global: bool,
}

#[derive(Args)]
struct TestCommand {
    /// Test files pattern
    pattern: Option<String>,
    
    /// Run in watch mode
    #[arg(short, long)]
    watch: bool,
}

#[derive(Args)]
struct StartCommand {
    /// Port number
    #[arg(short, long)]
    port: Option<u16>,
}

#[derive(Args)]
struct BuildCommand {
    /// Build for production
    #[arg(long)]
    production: bool,
    
    /// Watch for changes
    #[arg(short, long)]
    watch: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();
    
    // Handle global flags
    if cli.no_color {
        env::set_var("NO_COLOR", "1");
    }
    
    // Show banner for interactive commands
    if atty::is(atty::Stream::Stdout) {
        show_banner();
    }
    
    let result = match cli.command {
        Commands::Install(cmd) => handle_install(cmd, cli.verbose).await,
        Commands::Remove(cmd) => handle_remove(cmd, cli.verbose).await,
        Commands::Update(cmd) => handle_update(cmd, cli.verbose).await,
        Commands::List(cmd) => handle_list(cmd, cli.verbose).await,
        Commands::Show(cmd) => handle_show(cmd, cli.verbose).await,
        Commands::Search(cmd) => handle_search(cmd, cli.verbose).await,
        Commands::Run(cmd) => handle_run(cmd, cli.verbose).await,
        Commands::Init(cmd) => handle_init(cmd, cli.verbose).await,
        Commands::Clean(cmd) => handle_clean(cmd, cli.verbose).await,
        Commands::Config(cmd) => handle_config(cmd, cli.verbose).await,
        Commands::Publish(cmd) => handle_publish(cmd, cli.verbose).await,
        Commands::Login(cmd) => handle_login(cmd, cli.verbose).await,
        Commands::Logout(cmd) => handle_logout(cmd, cli.verbose).await,
        Commands::Audit(cmd) => handle_audit(cmd, cli.verbose).await,
        Commands::Link(cmd) => handle_link(cmd, cli.verbose).await,
        Commands::Create(cmd) => handle_create(cmd, cli.verbose).await,
        Commands::Exec(cmd) => handle_exec(cmd, cli.verbose).await,
        Commands::Tree(cmd) => handle_tree(cmd, cli.verbose).await,
        Commands::Outdated(cmd) => handle_outdated(cmd, cli.verbose).await,
        Commands::Test(cmd) => handle_test(cmd, cli.verbose).await,
        Commands::Start(cmd) => handle_start(cmd, cli.verbose).await,
        Commands::Build(cmd) => handle_build(cmd, cli.verbose).await,
    };
    
    if let Err(e) = result {
        eprintln!("{} {}", "error:".red().bold(), e);
        std::process::exit(1);
    }
    
    Ok(())
}
