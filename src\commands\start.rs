//! Start command implementation for development servers
//! 
//! This module handles starting development servers and applications.

use crate::commands::run::run_lifecycle_scripts;
use crate::types::PackageJson;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::path::Path;
use std::time::Instant;

pub async fn handle_start(cmd: crate::StartCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    let package_json_path = Path::new("package.json");
    
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    if verbose {
        println!("{} Starting development server...", "info".cyan().bold());
        if let Some(port) = cmd.port {
            println!("{} Using port: {}", "info".cyan(), port.to_string().bright_white());
        }
    }
    
    // Try different start strategies
    start_application(&package_json, &cmd, verbose).await?;
    
    let elapsed = start_time.elapsed();
    ui::show_success(&format!(
        "Start command completed in {:.2}s",
        elapsed.as_secs_f64()
    ));
    
    Ok(())
}

async fn start_application(
    package_json: &PackageJson,
    cmd: &crate::StartCommand,
    verbose: bool,
) -> Result<()> {
    // First, try to run the start script from package.json
    if let Some(ref scripts) = package_json.scripts {
        if let Some(start_script) = scripts.get("start") {
            if verbose {
                println!("{} Running start script from package.json", "info".cyan());
                println!("{} Command: {}", "info".cyan(), start_script.bright_white());
            }
            
            // Set PORT environment variable if specified
            if let Some(port) = cmd.port {
                std::env::set_var("PORT", port.to_string());
            }
            
            run_lifecycle_scripts("start", package_json, verbose).await?;
            return Ok(());
        }
        
        // Try dev script as fallback
        if let Some(dev_script) = scripts.get("dev") {
            if verbose {
                println!("{} No start script found, trying dev script", "info".cyan());
                println!("{} Command: {}", "info".cyan(), dev_script.bright_white());
            }
            
            if let Some(port) = cmd.port {
                std::env::set_var("PORT", port.to_string());
            }
            
            run_lifecycle_scripts("dev", package_json, verbose).await?;
            return Ok(());
        }
        
        // Try serve script as fallback
        if let Some(serve_script) = scripts.get("serve") {
            if verbose {
                println!("{} No start/dev script found, trying serve script", "info".cyan());
                println!("{} Command: {}", "info".cyan(), serve_script.bright_white());
            }
            
            if let Some(port) = cmd.port {
                std::env::set_var("PORT", port.to_string());
            }
            
            run_lifecycle_scripts("serve", package_json, verbose).await?;
            return Ok(());
        }
    }
    
    // If no start script, try to detect framework and suggest start method
    detect_and_suggest_start(package_json, cmd, verbose).await?;
    
    Ok(())
}

async fn detect_and_suggest_start(
    package_json: &PackageJson,
    cmd: &crate::StartCommand,
    verbose: bool,
) -> Result<()> {
    if verbose {
        println!("{} Detecting project type...", "info".cyan());
    }
    
    // Check for common frameworks and tools
    let mut detected_frameworks = Vec::new();
    
    // Check dependencies for framework detection
    if let Some(ref deps) = package_json.dependencies {
        if deps.contains_key("react") || deps.contains_key("react-dom") {
            detected_frameworks.push("React");
        }
        if deps.contains_key("vue") {
            detected_frameworks.push("Vue");
        }
        if deps.contains_key("angular") {
            detected_frameworks.push("Angular");
        }
        if deps.contains_key("express") {
            detected_frameworks.push("Express");
        }
        if deps.contains_key("fastify") {
            detected_frameworks.push("Fastify");
        }
        if deps.contains_key("next") {
            detected_frameworks.push("Next.js");
        }
        if deps.contains_key("nuxt") {
            detected_frameworks.push("Nuxt.js");
        }
        if deps.contains_key("svelte") {
            detected_frameworks.push("Svelte");
        }
    }
    
    // Check dev dependencies
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        if dev_deps.contains_key("vite") {
            detected_frameworks.push("Vite");
        }
        if dev_deps.contains_key("webpack") {
            detected_frameworks.push("Webpack");
        }
        if dev_deps.contains_key("parcel") {
            detected_frameworks.push("Parcel");
        }
        if dev_deps.contains_key("@vitejs/plugin-react") {
            detected_frameworks.push("React + Vite");
        }
        if dev_deps.contains_key("@vitejs/plugin-vue") {
            detected_frameworks.push("Vue + Vite");
        }
    }
    
    // Check for configuration files
    let config_files = [
        ("vite.config.js", "Vite"),
        ("vite.config.ts", "Vite"),
        ("webpack.config.js", "Webpack"),
        ("webpack.config.ts", "Webpack"),
        ("next.config.js", "Next.js"),
        ("nuxt.config.js", "Nuxt.js"),
        ("angular.json", "Angular CLI"),
        ("vue.config.js", "Vue CLI"),
        ("svelte.config.js", "SvelteKit"),
    ];
    
    for (config_file, framework) in &config_files {
        if Path::new(config_file).exists() {
            if !detected_frameworks.contains(framework) {
                detected_frameworks.push(framework);
            }
        }
    }
    
    if !detected_frameworks.is_empty() {
        println!("{} Detected frameworks: {}", 
            "✓".bright_green(),
            detected_frameworks.join(", ").bright_white()
        );
    }
    
    // Provide suggestions based on detected frameworks
    ui::show_warning("No start script found in package.json");
    
    println!();
    println!("{}", "💡 Suggested solutions:".bright_cyan().bold());
    println!();
    
    println!("  {} Add a start script to package.json:", "1.".bright_white().bold());
    
    if detected_frameworks.contains(&"React") && detected_frameworks.contains(&"Vite") {
        println!("     {}", r#""scripts": { "start": "vite", "dev": "vite" }"#.dimmed());
    } else if detected_frameworks.contains(&"Vue") && detected_frameworks.contains(&"Vite") {
        println!("     {}", r#""scripts": { "start": "vite", "dev": "vite" }"#.dimmed());
    } else if detected_frameworks.contains(&"Next.js") {
        println!("     {}", r#""scripts": { "start": "next start", "dev": "next dev" }"#.dimmed());
    } else if detected_frameworks.contains(&"Express") {
        println!("     {}", r#""scripts": { "start": "node index.js", "dev": "nodemon index.js" }"#.dimmed());
    } else if package_json.main.is_some() {
        let main_file = package_json.main.as_ref().unwrap();
        println!("     {}", format!(r#""scripts": {{ "start": "node {}" }}"#, main_file).dimmed());
    } else {
        println!("     {}", r#""scripts": { "start": "node index.js" }"#.dimmed());
    }
    
    println!();
    println!("  {} Install a development server:", "2.".bright_white().bold());
    
    if detected_frameworks.contains(&"React") || detected_frameworks.contains(&"Vue") {
        println!("     {} {} {}", "nx".bright_cyan(), "install".bright_green(), "--save-dev vite".bright_white());
    } else {
        println!("     {} {} {}", "nx".bright_cyan(), "install".bright_green(), "--save-dev nodemon".bright_white());
    }
    
    println!();
    println!("  {} Create entry point file:", "3.".bright_white().bold());
    
    if package_json.main.is_some() {
        println!("     {} Create {}", "•".dimmed(), package_json.main.as_ref().unwrap().bright_white());
    } else {
        println!("     {} Create index.js or src/index.js", "•".dimmed());
    }
    
    if let Some(port) = cmd.port {
        println!();
        println!("{} Note: Port {} will be used when available", 
            "📡".bright_blue(),
            port.to_string().bright_white()
        );
    }
    
    println!();
    
    Ok(())
}

// Helper function to check if server is running on a port
pub async fn check_port_availability(port: u16) -> bool {
    use std::net::TcpListener;
    
    TcpListener::bind(format!("127.0.0.1:{}", port)).is_ok()
}

// Helper function to find an available port
pub async fn find_available_port(start_port: u16) -> u16 {
    for port in start_port..start_port + 100 {
        if check_port_availability(port).await {
            return port;
        }
    }
    start_port // Fallback to original port
}
