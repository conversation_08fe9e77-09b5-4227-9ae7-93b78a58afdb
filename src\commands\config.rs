//! Config command implementation for managing configuration
//! 
//! This module handles reading and writing NX configuration settings.

use crate::types::NxConfig;
use crate::ui;
use anyhow::{Result, Context};
use colored::*;
use dirs;
use serde_json;
use std::collections::HashMap;
use std::path::PathBuf;
use tokio::fs;

pub async fn handle_config(cmd: crate::ConfigCommand, verbose: bool) -> Result<()> {
    if cmd.list {
        list_config(verbose).await?;
    } else if let Some(key) = cmd.key {
        if let Some(value) = cmd.value {
            set_config(&key, &value, verbose).await?;
        } else {
            get_config(&key, verbose).await?;
        }
    } else {
        list_config(verbose).await?;
    }
    
    Ok(())
}

async fn list_config(verbose: bool) -> Result<()> {
    let config = load_config().await?;
    
    println!();
    println!("{}", "⚙️ NX Configuration".bright_cyan().bold());
    println!("{}", "─".repeat(50).dimmed());
    
    // Registry configuration
    println!();
    println!("{}", "Registries:".bright_white().bold());
    for (name, registry) in &config.registries {
        println!("  {} {}", name.bright_green(), registry.url.bright_blue().underline());
        if registry.always_auth {
            println!("    {} {}", "Always Auth:".dimmed(), "true".bright_green());
        }
        if registry.auth_token.is_some() {
            println!("    {} {}", "Auth Token:".dimmed(), "***configured***".bright_yellow());
        }
    }
    
    // Global settings
    println!();
    println!("{}", "Global Settings:".bright_white().bold());
    
    if let Some(ref global_dir) = config.global_packages_dir {
        println!("  {} {}", "Global Packages:".dimmed(), global_dir.bright_blue());
    }
    
    if let Some(ref cache_dir) = config.cache_dir {
        println!("  {} {}", "Cache Directory:".dimmed(), cache_dir.bright_blue());
    }
    
    if let Some(downloads) = config.concurrent_downloads {
        println!("  {} {}", "Concurrent Downloads:".dimmed(), downloads.to_string().bright_white());
    }
    
    if let Some(installs) = config.concurrent_installs {
        println!("  {} {}", "Concurrent Installs:".dimmed(), installs.to_string().bright_white());
    }
    
    if let Some(timeout) = config.timeout {
        println!("  {} {}s", "Timeout:".dimmed(), timeout.to_string().bright_white());
    }
    
    if let Some(retries) = config.retry_attempts {
        println!("  {} {}", "Retry Attempts:".dimmed(), retries.to_string().bright_white());
    }
    
    println!("  {} {}", "Verify Signatures:".dimmed(), 
        if config.verify_signatures { "true".bright_green() } else { "false".bright_red() });
    
    println!("  {} {}", "Strict SSL:".dimmed(), 
        if config.strict_ssl { "true".bright_green() } else { "false".bright_red() });
    
    // Show config file location
    println!();
    println!("{} {}", "Config file:".dimmed(), 
        get_config_path()?.display().to_string().bright_blue().underline());
    
    println!();
    
    Ok(())
}

async fn get_config(key: &str, verbose: bool) -> Result<()> {
    let config = load_config().await?;
    
    let value = match key {
        "registry" => config.registries.get("npm")
            .map(|r| r.url.clone())
            .unwrap_or_default(),
        "cache-dir" => config.cache_dir.unwrap_or_default(),
        "global-dir" => config.global_packages_dir.unwrap_or_default(),
        "concurrent-downloads" => config.concurrent_downloads
            .map(|n| n.to_string())
            .unwrap_or_default(),
        "concurrent-installs" => config.concurrent_installs
            .map(|n| n.to_string())
            .unwrap_or_default(),
        "timeout" => config.timeout
            .map(|n| n.to_string())
            .unwrap_or_default(),
        "retry-attempts" => config.retry_attempts
            .map(|n| n.to_string())
            .unwrap_or_default(),
        "verify-signatures" => config.verify_signatures.to_string(),
        "strict-ssl" => config.strict_ssl.to_string(),
        _ => {
            ui::show_error(&format!("Unknown configuration key: {}", key));
            return Ok(());
        }
    };
    
    if value.is_empty() {
        ui::show_info(&format!("Configuration key '{}' is not set", key));
    } else {
        println!("{}", value.bright_white());
    }
    
    Ok(())
}

async fn set_config(key: &str, value: &str, verbose: bool) -> Result<()> {
    let mut config = load_config().await?;
    
    match key {
        "registry" => {
            if let Some(registry) = config.registries.get_mut("npm") {
                registry.url = value.to_string();
            } else {
                config.registries.insert("npm".to_string(), crate::types::RegistryConfig {
                    url: value.to_string(),
                    auth_token: None,
                    always_auth: false,
                });
            }
        },
        "cache-dir" => {
            config.cache_dir = Some(value.to_string());
        },
        "global-dir" => {
            config.global_packages_dir = Some(value.to_string());
        },
        "concurrent-downloads" => {
            let downloads = value.parse::<usize>()
                .context("Invalid number for concurrent-downloads")?;
            config.concurrent_downloads = Some(downloads);
        },
        "concurrent-installs" => {
            let installs = value.parse::<usize>()
                .context("Invalid number for concurrent-installs")?;
            config.concurrent_installs = Some(installs);
        },
        "timeout" => {
            let timeout = value.parse::<u64>()
                .context("Invalid number for timeout")?;
            config.timeout = Some(timeout);
        },
        "retry-attempts" => {
            let retries = value.parse::<usize>()
                .context("Invalid number for retry-attempts")?;
            config.retry_attempts = Some(retries);
        },
        "verify-signatures" => {
            let verify = value.parse::<bool>()
                .context("Invalid boolean for verify-signatures (use true/false)")?;
            config.verify_signatures = verify;
        },
        "strict-ssl" => {
            let strict = value.parse::<bool>()
                .context("Invalid boolean for strict-ssl (use true/false)")?;
            config.strict_ssl = strict;
        },
        _ => {
            ui::show_error(&format!("Unknown configuration key: {}", key));
            return Ok(());
        }
    }
    
    save_config(&config).await?;
    
    ui::show_success(&format!("Set {} = {}", key.bright_cyan(), value.bright_white()));
    
    Ok(())
}

async fn load_config() -> Result<NxConfig> {
    let config_path = get_config_path()?;
    
    if !config_path.exists() {
        // Create default config
        let config = NxConfig::default();
        save_config(&config).await?;
        return Ok(config);
    }
    
    let content = fs::read_to_string(&config_path).await
        .context("Failed to read config file")?;
    
    let config: NxConfig = serde_json::from_str(&content)
        .context("Failed to parse config file")?;
    
    Ok(config)
}

async fn save_config(config: &NxConfig) -> Result<()> {
    let config_path = get_config_path()?;
    
    // Ensure config directory exists
    if let Some(parent) = config_path.parent() {
        fs::create_dir_all(parent).await
            .context("Failed to create config directory")?;
    }
    
    let content = serde_json::to_string_pretty(config)
        .context("Failed to serialize config")?;
    
    fs::write(&config_path, content).await
        .context("Failed to write config file")?;
    
    Ok(())
}

fn get_config_path() -> Result<PathBuf> {
    let home_dir = dirs::home_dir()
        .context("Failed to get home directory")?;
    
    Ok(home_dir.join(".nx").join("config.json"))
}

// Helper functions for specific config operations
pub async fn get_registry_url() -> Result<String> {
    let config = load_config().await?;
    
    Ok(config.registries
        .get("npm")
        .map(|r| r.url.clone())
        .unwrap_or_else(|| "https://registry.npmjs.org".to_string()))
}

pub async fn get_auth_token(registry: &str) -> Option<String> {
    if let Ok(config) = load_config().await {
        return config.registries
            .get(registry)
            .and_then(|r| r.auth_token.clone());
    }
    None
}

pub async fn set_auth_token(registry: &str, token: &str) -> Result<()> {
    let mut config = load_config().await?;
    
    if let Some(registry_config) = config.registries.get_mut(registry) {
        registry_config.auth_token = Some(token.to_string());
    } else {
        config.registries.insert(registry.to_string(), crate::types::RegistryConfig {
            url: "https://registry.npmjs.org".to_string(),
            auth_token: Some(token.to_string()),
            always_auth: true,
        });
    }
    
    save_config(&config).await?;
    Ok(())
}

pub async fn remove_auth_token(registry: &str) -> Result<()> {
    let mut config = load_config().await?;
    
    if let Some(registry_config) = config.registries.get_mut(registry) {
        registry_config.auth_token = None;
        registry_config.always_auth = false;
    }
    
    save_config(&config).await?;
    Ok(())
}
