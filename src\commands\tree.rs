//! Tree command implementation for displaying dependency trees
//! 
//! This module handles displaying package dependency trees in a visual format.

use crate::types::PackageJson;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::collections::{HashMap, HashSet};
use std::path::Path;
use std::time::Instant;
use tokio::fs;

pub async fn handle_tree(cmd: crate::TreeCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    let package_json_path = Path::new("package.json");
    let node_modules_path = Path::new("node_modules");
    
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    if !node_modules_path.exists() {
        ui::show_info("No node_modules directory found. Run 'nx install' to install dependencies.");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    if verbose {
        println!("{} Building dependency tree...", "info".cyan().bold());
    }
    
    // Display project header
    display_project_header(&package_json);
    
    // Build and display dependency tree
    display_dependency_tree(&package_json, node_modules_path, &cmd, verbose).await?;
    
    let elapsed = start_time.elapsed();
    if verbose {
        println!();
        println!("{} Tree generated in {:.2}s", 
            "✨".bright_yellow(),
            elapsed.as_secs_f64()
        );
    }
    
    Ok(())
}

fn display_project_header(package_json: &PackageJson) {
    println!();
    
    if let Some(ref name) = package_json.name {
        print!("{}", name.bright_green().bold());
        if let Some(ref version) = package_json.version {
            print!(" {}", format!("v{}", version).bright_blue());
        }
        println!();
        
        if let Some(ref description) = package_json.description {
            println!("{}", description.white());
        }
    } else {
        println!("{}", "Project".bright_green().bold());
    }
    
    println!();
}

async fn display_dependency_tree(
    package_json: &PackageJson,
    node_modules_path: &Path,
    cmd: &crate::TreeCommand,
    verbose: bool,
) -> Result<()> {
    let mut tree_builder = DependencyTreeBuilder::new(node_modules_path, cmd.depth).await?;
    
    // Collect root dependencies
    let mut root_deps = Vec::new();
    
    if let Some(ref deps) = package_json.dependencies {
        if !cmd.production {
            for name in deps.keys() {
                root_deps.push((name.clone(), DependencyType::Production));
            }
        } else {
            for name in deps.keys() {
                root_deps.push((name.clone(), DependencyType::Production));
            }
        }
    }
    
    if !cmd.production {
        if let Some(ref dev_deps) = package_json.dev_dependencies {
            for name in dev_deps.keys() {
                root_deps.push((name.clone(), DependencyType::Development));
            }
        }
        
        if let Some(ref optional_deps) = package_json.optional_dependencies {
            for name in optional_deps.keys() {
                root_deps.push((name.clone(), DependencyType::Optional));
            }
        }
    }
    
    if root_deps.is_empty() {
        ui::show_info("No dependencies found");
        return Ok(());
    }
    
    // Sort dependencies
    root_deps.sort_by(|a, b| a.0.cmp(&b.0));
    
    // Display tree
    let mut visited = HashSet::new();
    
    for (index, (name, dep_type)) in root_deps.iter().enumerate() {
        let is_last = index == root_deps.len() - 1;
        tree_builder.display_package(name, dep_type, 0, is_last, "", &mut visited, verbose).await?;
    }
    
    // Display summary
    display_tree_summary(&tree_builder, &root_deps);
    
    Ok(())
}

#[derive(Debug, Clone)]
enum DependencyType {
    Production,
    Development,
    Optional,
    Peer,
}

impl DependencyType {
    fn to_string(&self) -> String {
        match self {
            DependencyType::Production => "prod".to_string(),
            DependencyType::Development => "dev".to_string(),
            DependencyType::Optional => "opt".to_string(),
            DependencyType::Peer => "peer".to_string(),
        }
    }
    
    fn color(&self) -> colored::ColoredString {
        match self {
            DependencyType::Production => self.to_string().bright_green(),
            DependencyType::Development => self.to_string().bright_yellow(),
            DependencyType::Optional => self.to_string().bright_magenta(),
            DependencyType::Peer => self.to_string().bright_cyan(),
        }
    }
}

struct DependencyTreeBuilder {
    node_modules_path: std::path::PathBuf,
    max_depth: Option<u32>,
    package_cache: HashMap<String, PackageInfo>,
    total_packages: usize,
    missing_packages: Vec<String>,
}

#[derive(Debug)]
struct PackageInfo {
    version: String,
    description: String,
    dependencies: HashMap<String, String>,
}

impl DependencyTreeBuilder {
    async fn new(node_modules_path: &Path, max_depth: Option<u32>) -> Result<Self> {
        Ok(Self {
            node_modules_path: node_modules_path.to_path_buf(),
            max_depth,
            package_cache: HashMap::new(),
            total_packages: 0,
            missing_packages: Vec::new(),
        })
    }
    
    async fn get_package_info(&mut self, name: &str) -> Option<&PackageInfo> {
        if self.package_cache.contains_key(name) {
            return self.package_cache.get(name);
        }
        
        let package_path = self.node_modules_path.join(name);
        let package_json_path = package_path.join("package.json");
        
        if !package_json_path.exists() {
            self.missing_packages.push(name.to_string());
            return None;
        }
        
        if let Ok(content) = fs::read_to_string(&package_json_path).await {
            if let Ok(pkg_json) = serde_json::from_str::<PackageJson>(&content) {
                let package_info = PackageInfo {
                    version: pkg_json.version.unwrap_or_else(|| "unknown".to_string()),
                    description: pkg_json.description.unwrap_or_default(),
                    dependencies: pkg_json.dependencies.unwrap_or_default(),
                };
                
                self.package_cache.insert(name.to_string(), package_info);
                self.total_packages += 1;
                
                return self.package_cache.get(name);
            }
        }
        
        None
    }
    
    async fn display_package(
        &mut self,
        name: &str,
        dep_type: &DependencyType,
        depth: u32,
        is_last: bool,
        prefix: &str,
        visited: &mut HashSet<String>,
        verbose: bool,
    ) -> Result<()> {
        // Check max depth
        if let Some(max_depth) = self.max_depth {
            if depth >= max_depth {
                return Ok(());
            }
        }
        
        // Create tree symbols
        let tree_symbol = if is_last { "└─" } else { "├─" };
        let child_prefix = if is_last { "  " } else { "│ " };
        
        // Get package info
        if let Some(package_info) = self.get_package_info(name).await {
            // Display package line
            println!("{}{} {} {} {}{}",
                prefix,
                tree_symbol.dimmed(),
                name.bright_white(),
                format!("v{}", package_info.version).bright_blue(),
                format!("[{}]", dep_type.color()),
                if verbose && !package_info.description.is_empty() {
                    format!(" {}", package_info.description.dimmed())
                } else {
                    String::new()
                }
            );
            
            // Check for circular dependencies
            let visit_key = format!("{}@{}", name, package_info.version);
            if visited.contains(&visit_key) {
                println!("{}{}   {} (circular dependency)",
                    prefix,
                    child_prefix,
                    "↻".bright_yellow()
                );
                return Ok(());
            }
            
            visited.insert(visit_key);
            
            // Display dependencies
            if !package_info.dependencies.is_empty() && depth < 10 {
                let mut deps: Vec<_> = package_info.dependencies.keys().collect();
                deps.sort();
                
                for (index, dep_name) in deps.iter().enumerate() {
                    let is_last_child = index == deps.len() - 1;
                    let new_prefix = format!("{}{}", prefix, child_prefix);
                    
                    Box::pin(self.display_package(
                        dep_name,
                        &DependencyType::Production,
                        depth + 1,
                        is_last_child,
                        &new_prefix,
                        visited,
                        verbose,
                    )).await?;
                }
            }
        } else {
            // Package not found
            println!("{}{} {} {}",
                prefix,
                tree_symbol.dimmed(),
                name.bright_red(),
                "MISSING".bright_red().bold()
            );
        }
        
        Ok(())
    }
}

fn display_tree_summary(
    tree_builder: &DependencyTreeBuilder,
    root_deps: &[(String, DependencyType)],
) {
    println!();
    println!("{}", "📊 Tree Summary".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    
    let production_count = root_deps.iter()
        .filter(|(_, t)| matches!(t, DependencyType::Production))
        .count();
    let dev_count = root_deps.iter()
        .filter(|(_, t)| matches!(t, DependencyType::Development))
        .count();
    let optional_count = root_deps.iter()
        .filter(|(_, t)| matches!(t, DependencyType::Optional))
        .count();
    
    if production_count > 0 {
        println!("  {} {} production dependencies", 
            "📦".bright_green(), 
            production_count.to_string().bright_white()
        );
    }
    
    if dev_count > 0 {
        println!("  {} {} development dependencies", 
            "🛠️".bright_yellow(), 
            dev_count.to_string().bright_white()
        );
    }
    
    if optional_count > 0 {
        println!("  {} {} optional dependencies", 
            "📋".bright_magenta(), 
            optional_count.to_string().bright_white()
        );
    }
    
    println!("  {} {} total packages installed", 
        "📊".bright_blue(), 
        tree_builder.total_packages.to_string().bright_white()
    );
    
    if !tree_builder.missing_packages.is_empty() {
        println!("  {} {} missing packages", 
            "⚠️".bright_red(), 
            tree_builder.missing_packages.len().to_string().bright_red()
        );
        
        if tree_builder.missing_packages.len() <= 5 {
            for missing in &tree_builder.missing_packages {
                println!("    • {}", missing.bright_red());
            }
        } else {
            for missing in tree_builder.missing_packages.iter().take(3) {
                println!("    • {}", missing.bright_red());
            }
            println!("    • {} ... and {} more", 
                "...".dimmed(),
                (tree_builder.missing_packages.len() - 3).to_string().dimmed()
            );
        }
    }
    
    println!();
}
