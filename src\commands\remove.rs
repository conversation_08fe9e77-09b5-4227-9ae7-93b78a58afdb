//! Remove command implementation with safe uninstallation
//! 
//! This module handles the removal of npm packages with dependency checking.

use crate::types::PackageJson;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::collections::HashSet;
use std::path::{Path, PathBuf};
use std::time::Instant;
use tokio::fs;

pub async fn handle_remove(cmd: crate::RemoveCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if cmd.packages.is_empty() {
        ui::show_error("No packages specified for removal");
        return Ok(());
    }
    
    if verbose {
        println!("{} Removing {} packages...", 
            "info".cyan().bold(),
            cmd.packages.len().to_string().bright_white().bold()
        );
    }
    
    if cmd.global {
        remove_global_packages(&cmd.packages, verbose).await?;
    } else {
        remove_local_packages(&cmd.packages, verbose).await?;
    }
    
    let elapsed = start_time.elapsed();
    ui::show_success(&format!(
        "Successfully removed {} packages in {:.2}s",
        cmd.packages.len(),
        elapsed.as_secs_f64()
    ));
    
    Ok(())
}

async fn remove_local_packages(packages: &[String], verbose: bool) -> Result<()> {
    let node_modules_path = Path::new("node_modules");
    let package_json_path = Path::new("package.json");
    
    // Read current package.json
    let mut package_json = if package_json_path.exists() {
        utils::read_package_json(Path::new(".")).await?
            .context("Failed to parse package.json")?
    } else {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    };
    
    let mut removed_packages = Vec::new();
    
    for package_name in packages {
        // Remove from package.json dependencies
        let mut found_in_deps = false;
        
        if let Some(ref mut deps) = package_json.dependencies {
            if deps.remove(package_name).is_some() {
                found_in_deps = true;
                if verbose {
                    println!("{} Removed {} from dependencies", 
                        "info".cyan(), package_name.bright_white());
                }
            }
        }
        
        if let Some(ref mut dev_deps) = package_json.dev_dependencies {
            if dev_deps.remove(package_name).is_some() {
                found_in_deps = true;
                if verbose {
                    println!("{} Removed {} from devDependencies", 
                        "info".cyan(), package_name.bright_white());
                }
            }
        }
        
        if let Some(ref mut optional_deps) = package_json.optional_dependencies {
            if optional_deps.remove(package_name).is_some() {
                found_in_deps = true;
                if verbose {
                    println!("{} Removed {} from optionalDependencies", 
                        "info".cyan(), package_name.bright_white());
                }
            }
        }
        
        if !found_in_deps {
            ui::show_warning(&format!("Package '{}' not found in package.json", package_name));
            continue;
        }
        
        // Remove from node_modules
        let package_path = node_modules_path.join(package_name);
        if package_path.exists() {
            fs::remove_dir_all(&package_path).await
                .with_context(|| format!("Failed to remove directory: {}", package_path.display()))?;
            
            if verbose {
                println!("{} Removed {} from node_modules", 
                    "info".cyan(), package_name.bright_white());
            }
        }
        
        removed_packages.push(package_name.clone());
    }
    
    // Write updated package.json
    if !removed_packages.is_empty() {
        let json_content = serde_json::to_string_pretty(&package_json)
            .context("Failed to serialize package.json")?;
        fs::write("package.json", json_content).await
            .context("Failed to write package.json")?;
        
        // Remove unused dependencies
        cleanup_unused_dependencies(&package_json, verbose).await?;
    }
    
    Ok(())
}

async fn remove_global_packages(packages: &[String], verbose: bool) -> Result<()> {
    let global_dir = utils::get_global_packages_dir()?;
    
    for package_name in packages {
        let package_path = global_dir.join(package_name);
        
        if package_path.exists() {
            fs::remove_dir_all(&package_path).await
                .with_context(|| format!("Failed to remove global package: {}", package_path.display()))?;
            
            if verbose {
                println!("{} Removed global package {}", 
                    "info".cyan(), package_name.bright_white());
            }
            
            // Remove from global bin if it exists
            remove_global_bin(package_name, &global_dir).await?;
        } else {
            ui::show_warning(&format!("Global package '{}' not found", package_name));
        }
    }
    
    Ok(())
}

async fn remove_global_bin(package_name: &str, global_dir: &Path) -> Result<()> {
    let bin_dir = global_dir.join("bin");
    
    if !bin_dir.exists() {
        return Ok(());
    }
    
    // Read the package.json of the removed package to find bin entries
    let package_json_path = global_dir.join(package_name).join("package.json");
    if !package_json_path.exists() {
        return Ok(());
    }
    
    let package_json_content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&package_json_content)?;
    
    if let Some(bin) = package_json.bin {
        match bin {
            crate::types::BinField::String(_) => {
                // Single binary with same name as package
                let bin_path = bin_dir.join(package_name);
                if bin_path.exists() {
                    fs::remove_file(&bin_path).await?;
                }
            },
            crate::types::BinField::Object(bins) => {
                // Multiple binaries
                for (bin_name, _) in bins {
                    let bin_path = bin_dir.join(&bin_name);
                    if bin_path.exists() {
                        fs::remove_file(&bin_path).await?;
                    }
                }
            }
        }
    }
    
    Ok(())
}

async fn cleanup_unused_dependencies(package_json: &PackageJson, verbose: bool) -> Result<()> {
    let node_modules_path = Path::new("node_modules");
    
    if !node_modules_path.exists() {
        return Ok(());
    }
    
    // Get all declared dependencies
    let mut declared_deps = HashSet::new();
    
    if let Some(ref deps) = package_json.dependencies {
        declared_deps.extend(deps.keys().cloned());
    }
    
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        declared_deps.extend(dev_deps.keys().cloned());
    }
    
    if let Some(ref optional_deps) = package_json.optional_dependencies {
        declared_deps.extend(optional_deps.keys().cloned());
    }
    
    // Get all installed packages
    let mut installed_packages = HashSet::new();
    let mut entries = fs::read_dir(node_modules_path).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let name = entry.file_name().to_string_lossy().to_string();
        
        if name.starts_with('.') {
            continue;
        }
        
        if name.starts_with('@') {
            // Scoped package directory
            let mut scoped_entries = fs::read_dir(entry.path()).await?;
            while let Some(scoped_entry) = scoped_entries.next_entry().await? {
                let scoped_name = scoped_entry.file_name().to_string_lossy().to_string();
                installed_packages.insert(format!("{}/{}", name, scoped_name));
            }
        } else {
            installed_packages.insert(name);
        }
    }
    
    // Find orphaned packages
    let orphaned: Vec<_> = installed_packages
        .difference(&declared_deps)
        .cloned()
        .collect();
    
    if !orphaned.is_empty() && verbose {
        println!("{} Found {} orphaned packages", 
            "info".cyan(), orphaned.len().to_string().bright_white());
        
        for package in &orphaned {
            println!("  {} {}", "•".dimmed(), package.dimmed());
        }
    }
    
    Ok(())
}
