{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 13228931636113154867, "path": 1456043686541092177, "deps": [[1542112352204983347, "build_script_build", false, 12841134952111746096], [2883436298747778685, "pki_types", false, 17328361246649027381], [3722963349756955755, "once_cell", false, 7996128489444516422], [5491919304041016563, "ring", false, 8050676742905948062], [6528079939221783635, "zeroize", false, 8303863176812282717], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 18214669146040670356], [17003143334332120809, "subtle", false, 6671976606189492963]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-61f751588a96b304\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}