//! Update command implementation for package updates
//! 
//! This module handles updating packages to their latest versions.

use crate::installer::PackageInstaller;
use crate::resolver::DependencyResolver;
use crate::types::{InstallOptions, PackageJson, UpdateInfo, UpdateType};
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use reqwest::Client;
use semver::Version;
use serde_json::Value;
use std::collections::HashMap;
use std::path::Path;
use std::time::Instant;

pub async fn handle_update(cmd: crate::UpdateCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if cmd.global {
        update_global_packages(&cmd.packages, cmd.check, verbose).await?;
    } else {
        update_local_packages(&cmd.packages, cmd.check, verbose).await?;
    }
    
    let elapsed = start_time.elapsed();
    ui::show_success(&format!(
        "Update operation completed in {:.2}s",
        elapsed.as_secs_f64()
    ));
    
    Ok(())
}

async fn update_local_packages(packages: &[String], check_only: bool, verbose: bool) -> Result<()> {
    let package_json_path = Path::new("package.json");
    
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    // Get current dependencies
    let mut current_deps = HashMap::new();
    
    if let Some(ref deps) = package_json.dependencies {
        for (name, version) in deps {
            current_deps.insert(name.clone(), (version.clone(), "dependency".to_string()));
        }
    }
    
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        for (name, version) in dev_deps {
            current_deps.insert(name.clone(), (version.clone(), "devDependency".to_string()));
        }
    }
    
    if current_deps.is_empty() {
        ui::show_info("No dependencies found to update");
        return Ok(());
    }
    
    // Filter packages to update
    let packages_to_update = if packages.is_empty() {
        current_deps.keys().cloned().collect::<Vec<_>>()
    } else {
        packages.to_vec()
    };
    
    if verbose {
        println!("{} Checking updates for {} packages...", 
            "info".cyan().bold(),
            packages_to_update.len().to_string().bright_white().bold()
        );
    }
    
    // Check for updates
    let updates = check_for_updates(&packages_to_update, &current_deps, verbose).await?;
    
    if updates.is_empty() {
        ui::show_success("All packages are up to date!");
        return Ok(());
    }
    
    // Display available updates
    display_update_summary(&updates);
    
    if check_only {
        return Ok(());
    }
    
    // Perform updates
    if !updates.is_empty() {
        perform_updates(&updates, &package_json, verbose).await?;
    }
    
    Ok(())
}

async fn update_global_packages(packages: &[String], check_only: bool, verbose: bool) -> Result<()> {
    let global_dir = utils::get_global_packages_dir()?;
    
    if !global_dir.exists() {
        ui::show_info("No global packages installed");
        return Ok(());
    }
    
    // Get list of global packages
    let mut global_packages = Vec::new();
    let mut entries = tokio::fs::read_dir(&global_dir).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let name = entry.file_name().to_string_lossy().to_string();
        
        if name.starts_with('.') || name == "bin" {
            continue;
        }
        
        let package_json_path = entry.path().join("package.json");
        if package_json_path.exists() {
            let content = tokio::fs::read_to_string(&package_json_path).await?;
            if let Ok(pkg_json) = serde_json::from_str::<PackageJson>(&content) {
                if let Some(version) = pkg_json.version {
                    global_packages.push((name, version));
                }
            }
        }
    }
    
    if global_packages.is_empty() {
        ui::show_info("No global packages found");
        return Ok(());
    }
    
    // Filter packages to update
    let packages_to_check = if packages.is_empty() {
        global_packages.iter().map(|(name, _)| name.clone()).collect()
    } else {
        packages.to_vec()
    };
    
    // Convert to the format expected by check_for_updates
    let current_versions: HashMap<String, (String, String)> = global_packages
        .iter()
        .map(|(name, version)| (name.clone(), (version.clone(), "global".to_string())))
        .collect();
    
    // Check for updates
    let updates = check_for_updates(&packages_to_check, &current_versions, verbose).await?;
    
    if updates.is_empty() {
        ui::show_success("All global packages are up to date!");
        return Ok(());
    }
    
    display_update_summary(&updates);
    
    if !check_only {
        // Update global packages
        for update in &updates {
            if verbose {
                println!("{} Updating global package {}...", 
                    "info".cyan(), update.name.bright_white());
            }
            
            // Use install command with global flag and force reinstall
            let install_cmd = crate::InstallCommand {
                packages: vec![format!("{}@{}", update.name, update.latest_version)],
                save_dev: false,
                save_optional: false,
                save_exact: false,
                global: true,
                dry_run: false,
                force: true,
                ignore_scripts: false,
                production: false,
            };
            
            crate::commands::install::handle_install(install_cmd, verbose).await?;
        }
    }
    
    Ok(())
}

async fn check_for_updates(
    packages: &[String],
    current_deps: &HashMap<String, (String, String)>,
    verbose: bool,
) -> Result<Vec<UpdateInfo>> {
    let client = Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .context("Failed to create HTTP client")?;
    
    let mut updates = Vec::new();
    
    let spinner = ui::create_resolution_spinner();
    spinner.set_message("Checking for package updates...");
    
    for package_name in packages {
        if let Some((current_version, _)) = current_deps.get(package_name) {
            if let Some(update_info) = check_package_update(&client, package_name, current_version).await? {
                updates.push(update_info);
            }
        }
    }
    
    spinner.finish_and_clear();
    
    Ok(updates)
}

async fn check_package_update(
    client: &Client,
    package_name: &str,
    current_version: &str,
) -> Result<Option<UpdateInfo>> {
    let url = format!("https://registry.npmjs.org/{}", urlencoding::encode(package_name));
    
    let response = client
        .get(&url)
        .header("User-Agent", "nx/0.2.0")
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Ok(None);
    }
    
    let manifest: Value = response.json().await?;
    
    let dist_tags = manifest.get("dist-tags").and_then(|t| t.as_object());
    let latest_version = dist_tags
        .and_then(|tags| tags.get("latest"))
        .and_then(|v| v.as_str())
        .unwrap_or("0.0.0");
    
    // Parse versions
    let current_clean = clean_version_spec(current_version);
    let current_ver = Version::parse(&current_clean).ok()?;
    let latest_ver = Version::parse(latest_version).ok()?;
    
    if latest_ver > current_ver {
        let update_type = determine_update_type(&current_ver, &latest_ver);
        
        Ok(Some(UpdateInfo {
            name: package_name.to_string(),
            current_version: current_ver.to_string(),
            latest_version: latest_ver.to_string(),
            wanted_version: latest_version.to_string(),
            update_type,
        }))
    } else {
        Ok(None)
    }
}

fn clean_version_spec(version_spec: &str) -> String {
    // Remove version prefixes like ^, ~, >=, etc.
    version_spec
        .trim_start_matches('^')
        .trim_start_matches('~')
        .trim_start_matches(">=")
        .trim_start_matches("<=")
        .trim_start_matches('>')
        .trim_start_matches('<')
        .trim_start_matches('=')
        .to_string()
}

fn determine_update_type(current: &Version, latest: &Version) -> UpdateType {
    if latest.major > current.major {
        UpdateType::Major
    } else if latest.minor > current.minor {
        UpdateType::Minor
    } else if latest.patch > current.patch {
        UpdateType::Patch
    } else {
        UpdateType::Prerelease
    }
}

fn display_update_summary(updates: &[UpdateInfo]) {
    println!();
    println!("{} Available updates:", "📦".bright_cyan());
    println!();
    
    let mut major_updates = Vec::new();
    let mut minor_updates = Vec::new();
    let mut patch_updates = Vec::new();
    
    for update in updates {
        match update.update_type {
            UpdateType::Major => major_updates.push(update),
            UpdateType::Minor => minor_updates.push(update),
            UpdateType::Patch => patch_updates.push(update),
            UpdateType::Prerelease => patch_updates.push(update),
        }
    }
    
    if !major_updates.is_empty() {
        println!("{} {} major updates:", 
            "🔴".bright_red(), major_updates.len().to_string().bright_red().bold());
        for update in major_updates {
            println!("  {} {} → {}",
                update.name.bright_white(),
                update.current_version.bright_red(),
                update.latest_version.bright_green()
            );
        }
        println!();
    }
    
    if !minor_updates.is_empty() {
        println!("{} {} minor updates:", 
            "🟡".bright_yellow(), minor_updates.len().to_string().bright_yellow().bold());
        for update in minor_updates {
            println!("  {} {} → {}",
                update.name.bright_white(),
                update.current_version.bright_yellow(),
                update.latest_version.bright_green()
            );
        }
        println!();
    }
    
    if !patch_updates.is_empty() {
        println!("{} {} patch updates:", 
            "🟢".bright_green(), patch_updates.len().to_string().bright_green().bold());
        for update in patch_updates {
            println!("  {} {} → {}",
                update.name.bright_white(),
                update.current_version.bright_blue(),
                update.latest_version.bright_green()
            );
        }
        println!();
    }
}

async fn perform_updates(
    updates: &[UpdateInfo],
    package_json: &PackageJson,
    verbose: bool,
) -> Result<()> {
    if verbose {
        println!("{} Installing {} updates...", 
            "info".cyan().bold(),
            updates.len().to_string().bright_white().bold()
        );
    }
    
    // Create install command for updates
    let packages_to_install: Vec<String> = updates
        .iter()
        .map(|update| format!("{}@{}", update.name, update.latest_version))
        .collect();
    
    let install_cmd = crate::InstallCommand {
        packages: packages_to_install,
        save_dev: false,
        save_optional: false,
        save_exact: false,
        global: false,
        dry_run: false,
        force: true,
        ignore_scripts: false,
        production: false,
    };
    
    // Use the install handler to perform the updates
    crate::commands::install::handle_install(install_cmd, verbose).await?;
    
    Ok(())
}
