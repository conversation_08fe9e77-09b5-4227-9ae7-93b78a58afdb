//! Beautiful terminal UI with enhanced progress bars and styling
//! 
//! This module provides all the user interface components including progress bars,
//! spinners, tables, and status messages with beautiful coloring and animations.

use colored::*;
use indicatif::{ProgressBar, ProgressStyle, MultiProgress};
use std::time::{Duration, Instant};
use std::sync::Arc;
use std::io::{self, Write};
use std::collections::HashMap;

// ===== PACKAGE STATUS AND TYPES =====

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PackageStatus {
    Installing,
    Installed,
    Updated,
    Skipped,
    Failed,
}

#[derive(Debug, Clone)]
pub struct InstallationStats {
    pub total: usize,
    pub installed: usize,
    pub updated: usize,
    pub skipped: usize,
    pub failed: usize,
    pub start_time: Instant,
}

impl InstallationStats {
    pub fn new(total: usize) -> Self {
        Self {
            total,
            installed: 0,
            updated: 0,
            skipped: 0,
            failed: 0,
            start_time: Instant::now(),
        }
    }
}

#[derive(Debug, <PERSON>lone)]
pub struct VulnerabilityInfo {
    pub severity: String,
    pub title: String,
    pub package: String,
    pub version: String,
    pub patched_in: Option<String>,
    pub overview: String,
}

// ===== BANNER AND BASIC STATUS MESSAGES =====

/// Show the enhanced NX banner
pub fn show_banner() {
    println!("{}", "🚀 NX Package Manager".bright_cyan().bold());
    println!("{}", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━".bright_cyan());
    println!("{}", "⚡ Ultra-fast • 🦀 Built in Rust • 📦 npm Compatible".bright_blue());
    println!();
}

/// Show informational message with icon
pub fn show_info(message: &str) {
    println!("{} {}", "ℹ️".bright_blue(), message.white());
}

/// Show success message with checkmark
pub fn show_success(message: &str) {
    println!("{} {}", "✅".bright_green(), message.bright_green());
}

/// Show warning message
pub fn show_warning(message: &str) {
    println!("{} {}", "⚠️".bright_yellow(), message.bright_yellow());
}

/// Show error message with formatting
pub fn show_error(message: &str) {
    eprintln!("{} {}", "❌ Error:".bright_red().bold(), message.bright_red());
}

/// Create a spinner for long-running operations
pub fn show_spinner(message: &str) {
    println!("{} {}", "🔄".bright_blue(), message.bright_white());
}

// ===== PROGRESS BARS AND SPINNERS =====

/// Create a beautiful progress bar for downloads
pub fn create_download_progress(total: u64, package_name: &str) -> ProgressBar {
    let pb = ProgressBar::new(total);
    pb.set_style(
        ProgressStyle::default_bar()
            .template("📥 {msg} [{elapsed_precise}] {bar:40.cyan/blue} {bytes}/{total_bytes} ({bytes_per_sec}, {eta})")
            .expect("Template should be valid")
            .progress_chars("█▉▊▋▌▍▎▏  ")
    );
    pb.set_message(format!("Downloading {}", package_name.bright_white().bold()));
    pb
}

/// Create a progress bar for installations
pub fn create_install_progress(total: u64) -> ProgressBar {
    let pb = ProgressBar::new(total);
    pb.set_style(
        ProgressStyle::default_bar()
            .template("📦 Installing packages [{elapsed_precise}] {bar:40.green/blue} {pos}/{len} {msg}")
            .expect("Template should be valid")
            .progress_chars("█▉▊▋▌▍▎▏  ")
    );
    pb
}

/// Create a spinner for resolution
pub fn create_resolution_spinner() -> ProgressBar {
    let pb = ProgressBar::new_spinner();
    pb.enable_steady_tick(Duration::from_millis(120));
    pb.set_style(
        ProgressStyle::default_spinner()
            .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
            .template("🔍 {spinner:.blue} {msg}")
            .expect("Template should be valid")
    );
    pb
}

/// Create multi-progress for parallel operations
pub fn create_multi_progress() -> Arc<MultiProgress> {
    Arc::new(MultiProgress::new())
}

/// Create a loading animation for generic operations
pub fn create_loading_spinner(message: &str) -> ProgressBar {
    let pb = ProgressBar::new_spinner();
    pb.enable_steady_tick(Duration::from_millis(100));
    pb.set_style(
        ProgressStyle::default_spinner()
            .tick_strings(&["⠄", "⠆", "⠇", "⠋", "⠙", "⠸", "⠰", "⠠", "⠰", "⠸", "⠙", "⠋", "⠇", "⠆"])
            .template("⏳ {spinner:.cyan} {msg}")
            .expect("Template should be valid")
    );
    pb.set_message(message.to_string());
    pb
}

/// Create progress bar for extraction operations
pub fn create_extraction_progress(total: u64) -> ProgressBar {
    let pb = ProgressBar::new(total);
    pb.set_style(
        ProgressStyle::default_bar()
            .template("📦 Extracting [{elapsed_precise}] {bar:40.yellow/blue} {pos}/{len} {msg}")
            .expect("Template should be valid")
            .progress_chars("█▉▊▋▌▍▎▏  ")
    );
    pb
}

// ===== UTILITY FUNCTIONS =====

/// Format bytes in human-readable format
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    if bytes == 0 {
        return "0 B".to_string();
    }
    
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Format duration in human-readable format
pub fn format_duration(duration: Duration) -> String {
    let total_ms = duration.as_millis();
    
    if total_ms < 1000 {
        format!("{}ms", total_ms)
    } else if total_ms < 60_000 {
        format!("{:.1}s", total_ms as f64 / 1000.0)
    } else {
        let minutes = total_ms / 60_000;
        let seconds = (total_ms % 60_000) as f64 / 1000.0;
        format!("{}m {:.1}s", minutes, seconds)
    }
}

/// Format package count with appropriate singular/plural
pub fn format_package_count(count: usize) -> String {
    if count == 1 {
        "1 package".to_string()
    } else {
        format!("{} packages", count)
    }
}

// ===== TABLE FORMATTING =====

/// Show a beautiful table header
pub fn show_table_header(columns: &[&str], widths: &[usize]) {
    let top_border = columns.iter().zip(widths.iter())
        .map(|(_, &width)| "─".repeat(width))
        .collect::<Vec<_>>()
        .join("┬");
    
    println!("┌{}┐", top_border);
    
    let header_row = columns.iter().zip(widths.iter())
        .map(|(col, &width)| format!(" {:<width$} ", col.bright_white().bold(), width = width - 2))
        .collect::<Vec<_>>()
        .join("│");
    
    println!("│{}│", header_row);
    
    let separator = columns.iter().zip(widths.iter())
        .map(|(_, &width)| "─".repeat(width))
        .collect::<Vec<_>>()
        .join("┼");
    
    println!("├{}┤", separator);
}

/// Show a table row
pub fn show_table_row(values: &[&str], widths: &[usize]) {
    let row = values.iter().zip(widths.iter())
        .map(|(val, &width)| format!(" {:<width$} ", val, width = width - 2))
        .collect::<Vec<_>>()
        .join("│");
    
    println!("│{}│", row);
}

/// Show table footer
pub fn show_table_footer(widths: &[usize]) {
    let bottom_border = widths.iter()
        .map(|&width| "─".repeat(width))
        .collect::<Vec<_>>()
        .join("┴");
    
    println!("└{}┘", bottom_border);
}

/// Display a formatted table with data
pub fn display_table(headers: &[&str], rows: &[Vec<String>], column_widths: Option<&[usize]>) {
    if rows.is_empty() {
        return;
    }

    // Calculate column widths if not provided
    let widths = if let Some(widths) = column_widths {
        widths.to_vec()
    } else {
        let mut widths = headers.iter().map(|h| h.len() + 2).collect::<Vec<_>>();
        for row in rows {
            for (i, cell) in row.iter().enumerate() {
                if i < widths.len() {
                    widths[i] = widths[i].max(cell.len() + 2);
                }
            }
        }
        widths
    };

    show_table_header(headers, &widths);
    
    for row in rows {
        let row_strs: Vec<&str> = row.iter().map(|s| s.as_str()).collect();
        show_table_row(&row_strs, &widths);
    }
    
    show_table_footer(&widths);
}

// ===== PACKAGE STATUS AND INSTALLATION TRACKING =====

/// Show package installation status
pub fn show_package_status(name: &str, version: &str, status: PackageStatus) {
    let (icon, color_fn): (&str, fn(&str) -> ColoredString) = match status {
        PackageStatus::Installing => ("📦", |s| s.bright_blue()),
        PackageStatus::Installed => ("✅", |s| s.bright_green()),
        PackageStatus::Updated => ("🔄", |s| s.bright_yellow()),
        PackageStatus::Skipped => ("⭐", |s| s.bright_cyan()),
        PackageStatus::Failed => ("❌", |s| s.bright_red()),
    };
    
    println!("{} {} {}@{}", icon, color_fn(""), name, version);
}

/// Show package status with additional info
pub fn show_package_status_detailed(name: &str, version: &str, status: PackageStatus, info: Option<&str>) {
    let (icon, color_fn): (&str, fn(&str) -> ColoredString) = match status {
        PackageStatus::Installing => ("📦", |s| s.bright_blue()),
        PackageStatus::Installed => ("✅", |s| s.bright_green()),
        PackageStatus::Updated => ("🔄", |s| s.bright_yellow()),
        PackageStatus::Skipped => ("⭐", |s| s.bright_cyan()),
        PackageStatus::Failed => ("❌", |s| s.bright_red()),
    };
    
    if let Some(info) = info {
        println!("{} {} {}@{} {}", icon, color_fn(""), name, version, info.dimmed());
    } else {
        println!("{} {} {}@{}", icon, color_fn(""), name, version);
    }
}

// ===== RESOLUTION AND SUMMARY DISPLAYS =====

/// Show dependency resolution summary
pub fn show_resolution_summary(total: usize, added: usize, updated: usize, unchanged: usize) {
    println!();
    println!("{}", "📊 Resolution Summary:".bright_cyan().bold());
    println!("┌─────────────────────────────────────────┐");
    println!("│ {:<39} │", format!("Total packages: {}", total.to_string().bright_white().bold()));
    if added > 0 {
        println!("│ {:<39} │", format!("Added: {}", added.to_string().bright_green().bold()));
    }
    if updated > 0 {
        println!("│ {:<39} │", format!("Updated: {}", updated.to_string().bright_yellow().bold()));
    }
    if unchanged > 0 {
        println!("│ {:<39} │", format!("Unchanged: {}", unchanged.to_string().bright_blue().bold()));
    }
    println!("└─────────────────────────────────────────┘");
    println!();
}

/// Show installation summary with timing
pub fn show_installation_summary(stats: &InstallationStats) {
    let duration = stats.start_time.elapsed();
    println!();
    println!("{}", "📦 Installation Summary:".bright_cyan().bold());
    println!("┌─────────────────────────────────────────┐");
    println!("│ {:<39} │", format!("Total: {}", stats.total.to_string().bright_white().bold()));
    if stats.installed > 0 {
        println!("│ {:<39} │", format!("✅ Installed: {}", stats.installed.to_string().bright_green().bold()));
    }
    if stats.updated > 0 {
        println!("│ {:<39} │", format!("🔄 Updated: {}", stats.updated.to_string().bright_yellow().bold()));
    }
    if stats.skipped > 0 {
        println!("│ {:<39} │", format!("⭐ Skipped: {}", stats.skipped.to_string().bright_cyan().bold()));
    }
    if stats.failed > 0 {
        println!("│ {:<39} │", format!("❌ Failed: {}", stats.failed.to_string().bright_red().bold()));
    }
    println!("│ {:<39} │", format!("⏱️ Duration: {}", format_duration(duration).bright_magenta().bold()));
    println!("└─────────────────────────────────────────┘");
    println!();
}

/// Show timing information
pub fn show_timing(operation: &str, duration: Duration) {
    println!("{} {} in {}", 
        "⏱️".bright_magenta(), 
        operation.bright_white(), 
        format_duration(duration).bright_cyan().bold()
    );
}

// ===== INTERACTIVE PROMPTS =====

/// Prompt user for yes/no confirmation
pub fn prompt_yes_no(message: &str, default: bool) -> io::Result<bool> {
    let default_str = if default { "[Y/n]" } else { "[y/N]" };
    print!("{} {} {}: ", "❓".bright_yellow(), message.white(), default_str.dimmed());
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim().to_lowercase();
    
    match input.as_str() {
        "y" | "yes" => Ok(true),
        "n" | "no" => Ok(false),
        "" => Ok(default),
        _ => prompt_yes_no(message, default), // Ask again on invalid input
    }
}

/// Prompt user for text input
pub fn prompt_input(message: &str, default: Option<&str>) -> io::Result<String> {
    if let Some(default) = default {
        print!("{} {} ({}): ", "❓".bright_yellow(), message.white(), default.dimmed());
    } else {
        print!("{} {}: ", "❓".bright_yellow(), message.white());
    }
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();
    
    if input.is_empty() {
        if let Some(default) = default {
            Ok(default.to_string())
        } else {
            prompt_input(message, default) // Ask again if no default and empty input
        }
    } else {
        Ok(input.to_string())
    }
}

/// Prompt user to select from a list
pub fn prompt_select(message: &str, options: &[&str]) -> io::Result<usize> {
    println!("{} {}", "❓".bright_yellow(), message.white());
    for (i, option) in options.iter().enumerate() {
        println!("  {}. {}", (i + 1).to_string().bright_cyan(), option);
    }
    print!("Select (1-{}): ", options.len());
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    
    match input.trim().parse::<usize>() {
        Ok(n) if n > 0 && n <= options.len() => Ok(n - 1),
        _ => {
            show_error("Invalid selection. Please try again.");
            prompt_select(message, options)
        }
    }
}

// ===== TREE DISPLAY FORMATTING =====

/// Display a dependency tree
pub fn display_dependency_tree(name: &str, version: &str, dependencies: &HashMap<String, String>, depth: usize, is_last: bool, prefix: String) {
    let current_prefix = if depth == 0 {
        "".to_string()
    } else if is_last {
        format!("{}└── ", prefix)
    } else {
        format!("{}├── ", prefix)
    };
    
    let package_display = format!("{}@{}", name.bright_white().bold(), version.bright_cyan());
    println!("{}{}", current_prefix, package_display);
    
    if depth < 3 { // Limit depth to prevent infinite recursion
        let dep_names: Vec<_> = dependencies.keys().collect();
        for (i, dep_name) in dep_names.iter().enumerate() {
            let is_last_dep = i == dep_names.len() - 1;
            let new_prefix = if depth == 0 {
                "".to_string()
            } else if is_last {
                format!("{}    ", prefix)
            } else {
                format!("{}│   ", prefix)
            };
            
            if let Some(dep_version) = dependencies.get(*dep_name) {
                display_dependency_tree(dep_name, dep_version, &HashMap::new(), depth + 1, is_last_dep, new_prefix);
            }
        }
    }
}

/// Display project information header
pub fn display_project_header(name: &str, version: &str, path: &str) {
    println!();
    println!("{} {}", "📦".bright_cyan(), "Project Information".bright_cyan().bold());
    println!("┌─────────────────────────────────────────┐");
    println!("│ {:<39} │", format!("Name: {}", name.bright_white().bold()));
    println!("│ {:<39} │", format!("Version: {}", version.bright_cyan()));
    println!("│ {:<39} │", format!("Path: {}", path.dimmed()));
    println!("└─────────────────────────────────────────┘");
    println!();
}

/// Display tree summary
pub fn display_tree_summary(total_deps: usize, dev_deps: usize, optional_deps: usize) {
    println!();
    println!("{}", "📊 Dependency Summary:".bright_cyan().bold());
    println!("┌─────────────────────────────────────────┐");
    println!("│ {:<39} │", format!("Total dependencies: {}", total_deps.to_string().bright_white().bold()));
    if dev_deps > 0 {
        println!("│ {:<39} │", format!("Development: {}", dev_deps.to_string().bright_yellow()));
    }
    if optional_deps > 0 {
        println!("│ {:<39} │", format!("Optional: {}", optional_deps.to_string().bright_blue()));
    }
    println!("└─────────────────────────────────────────┘");
    println!();
}

// ===== PACKAGE DISPLAY FUNCTIONS =====

/// Display package information
pub fn display_package_info(name: &str, version: &str, description: Option<&str>, author: Option<&str>, license: Option<&str>, homepage: Option<&str>) {
    println!();
    println!("{} {}", "📦".bright_cyan(), name.bright_white().bold());
    println!("{}", "─".repeat(60).bright_cyan());
    println!("{}: {}", "Version".bright_blue(), version.bright_white());
    
    if let Some(desc) = description {
        println!("{}: {}", "Description".bright_blue(), desc.white());
    }
    
    if let Some(author) = author {
        println!("{}: {}", "Author".bright_blue(), author.white());
    }
    
    if let Some(license) = license {
        println!("{}: {}", "License".bright_blue(), license.white());
    }
    
    if let Some(homepage) = homepage {
        println!("{}: {}", "Homepage".bright_blue(), homepage.bright_cyan().underline());
    }
    
    println!();
}

/// Display package search result
pub fn display_package_result(name: &str, version: &str, description: Option<&str>, downloads: Option<u64>, updated: Option<&str>) {
    println!("┌─────────────────────────────────────────────────────────────┐");
    println!("│ {} │", format!("{:<59}", format!("{} {}", name.bright_white().bold(), format!("v{}", version).bright_cyan())));
    
    if let Some(desc) = description {
        let truncated = if desc.len() > 57 {
            format!("{}...", &desc[..54])
        } else {
            desc.to_string()
        };
        println!("│ {} │", format!("{:<59}", truncated.dimmed()));
    }
    
    let mut meta_info = Vec::new();
    if let Some(downloads) = downloads {
        meta_info.push(format!("📥 {}", format_downloads(downloads)));
    }
    if let Some(updated) = updated {
        meta_info.push(format!("🕒 {}", updated));
    }
    
    if !meta_info.is_empty() {
        let meta_str = meta_info.join(" • ");
        let truncated = if meta_str.len() > 57 {
            format!("{}...", &meta_str[..54])
        } else {
            meta_str
        };
        println!("│ {} │", format!("{:<59}", truncated.bright_blue()));
    }
    
    println!("└─────────────────────────────────────────────────────────────┘");
}

/// Format download count
fn format_downloads(downloads: u64) -> String {
    if downloads >= 1_000_000 {
        format!("{:.1}M", downloads as f64 / 1_000_000.0)
    } else if downloads >= 1_000 {
        format!("{:.1}K", downloads as f64 / 1_000.0)
    } else {
        downloads.to_string()
    }
}

// ===== AUDIT AND VULNERABILITY DISPLAY =====

/// Display audit results summary
pub fn display_audit_results(vulnerabilities: &[VulnerabilityInfo], total_packages: usize) {
    println!();
    println!("{} {}", "🔍".bright_cyan(), "Security Audit Results".bright_cyan().bold());
    println!("┌─────────────────────────────────────────┐");
    println!("│ {:<39} │", format!("Packages audited: {}", total_packages.to_string().bright_white().bold()));
    let vuln_count = vulnerabilities.len();
    let vuln_text = if vuln_count == 0 {
        vuln_count.to_string().bright_green().bold()
    } else {
        vuln_count.to_string().bright_red().bold()
    };
    println!("│ {:<39} │", format!("Vulnerabilities found: {}", vuln_text));
    println!("└─────────────────────────────────────────┘");
    
    if !vulnerabilities.is_empty() {
        println!();
        for vuln in vulnerabilities {
            display_vulnerability_details(vuln);
        }
    } else {
        println!();
        show_success("No vulnerabilities found! Your dependencies are secure. 🛡️");
    }
}

/// Display vulnerability details
pub fn display_vulnerability_details(vuln: &VulnerabilityInfo) {
    let severity_color = match vuln.severity.to_lowercase().as_str() {
        "critical" => |s: &str| s.bright_red().bold(),
        "high" => |s: &str| s.red().bold(),
        "moderate" => |s: &str| s.yellow().bold(),
        "low" => |s: &str| s.blue(),
        _ => |s: &str| s.white(),
    };
    
    println!("┌─────────────────────────────────────────────────────────────┐");
    println!("│ {} │", format!("{:<59}", format!("{} {}", "⚠️".bright_red(), vuln.title.bright_white().bold())));
    println!("│ {} │", format!("{:<59}", format!("Package: {}@{}", vuln.package.bright_cyan(), vuln.version.bright_white())));
    println!("│ {} │", format!("{:<59}", format!("Severity: {}", severity_color(&vuln.severity))));
    
    if let Some(patched) = &vuln.patched_in {
        println!("│ {} │", format!("{:<59}", format!("Patched in: {}", patched.bright_green())));
    }
    
    if !vuln.overview.is_empty() {
        let words: Vec<&str> = vuln.overview.split_whitespace().collect();
        let mut lines = Vec::new();
        let mut current_line = String::new();
        
        for word in words {
            if current_line.len() + word.len() + 1 > 57 {
                lines.push(current_line.clone());
                current_line = word.to_string();
            } else {
                if !current_line.is_empty() {
                    current_line.push(' ');
                }
                current_line.push_str(word);
            }
        }
        if !current_line.is_empty() {
            lines.push(current_line);
        }
        
        for line in lines {
            println!("│ {} │", format!("{:<59}", line.dimmed()));
        }
    }
    
    println!("└─────────────────────────────────────────────────────────────┘");
    println!();
}

// ===== TEMPLATE AND CREATION DISPLAYS =====

/// Show available templates
pub fn show_available_templates() {
    println!();
    println!("{} {}", "📋".bright_cyan(), "Available Templates:".bright_cyan().bold());
    println!("┌─────────────────────────────────────────────────────────────┐");
    println!("│ {} │", format!("{:<59}", "react       - React application with TypeScript".bright_white()));
    println!("│ {} │", format!("{:<59}", "vue         - Vue.js application with TypeScript".bright_white()));
    println!("│ {} │", format!("{:<59}", "node        - Node.js application".bright_white()));
    println!("│ {} │", format!("{:<59}", "typescript  - TypeScript library".bright_white()));
    println!("└─────────────────────────────────────────────────────────────┘");
    println!();
}

/// Show next steps after initialization
pub fn show_next_steps(project_name: &str) {
    println!();
    println!("{} {}", "🎯".bright_cyan(), "Next Steps:".bright_cyan().bold());
    println!("┌─────────────────────────────────────────────────────────────┐");
    println!("│ {} │", format!("{:<59}", format!("1. cd {}", project_name).bright_white()));
    println!("│ {} │", format!("{:<59}", "2. nx install              # Install dependencies".bright_white()));
    println!("│ {} │", format!("{:<59}", "3. nx run start            # Start development".bright_white()));
    println!("└─────────────────────────────────────────────────────────────┘");
    println!();
}

// ===== CACHE AND CLEANUP DISPLAYS =====

/// Display cache information
pub fn display_cache_info(total_size: u64, package_count: usize, oldest_package: Option<&str>) {
    println!();
    println!("{} {}", "💾".bright_cyan(), "Cache Information:".bright_cyan().bold());
    println!("┌─────────────────────────────────────────┐");
    println!("│ {:<39} │", format!("Total size: {}", format_bytes(total_size).bright_white().bold()));
    println!("│ {:<39} │", format!("Cached packages: {}", package_count.to_string().bright_cyan()));
    if let Some(oldest) = oldest_package {
        println!("│ {:<39} │", format!("Oldest package: {}", oldest.dimmed()));
    }
    println!("└─────────────────────────────────────────┘");
    println!();
}

/// Show cleanup information
pub fn show_cleanup_info(items_cleaned: usize, space_freed: u64) {
    println!();
    println!("{} {}", "🧹".bright_green(), "Cleanup Complete:".bright_green().bold());
    println!("┌─────────────────────────────────────────┐");
    println!("│ {:<39} │", format!("Items cleaned: {}", items_cleaned.to_string().bright_white().bold()));
    println!("│ {:<39} │", format!("Space freed: {}", format_bytes(space_freed).bright_green().bold()));
    println!("└─────────────────────────────────────────┘");
    println!();
}

/// Show login status
pub fn show_login_status(registries: &HashMap<String, String>) {
    println!();
    println!("{} {}", "🔐".bright_cyan(), "Login Status:".bright_cyan().bold());
    
    if registries.is_empty() {
        println!("Not logged in to any registries");
        return;
    }
    
    println!("┌─────────────────────────────────────────────────────────────┐");
    for (registry, username) in registries {
        println!("│ {} │", format!("{:<59}", format!("✅ {} ({})", registry.bright_white(), username.bright_cyan())));
    }
    println!("└─────────────────────────────────────────────────────────────┘");
    println!();
}

// ===== UTILITY EXTENSION TRAITS =====
// Removed the problematic trait since it was causing compilation issues
