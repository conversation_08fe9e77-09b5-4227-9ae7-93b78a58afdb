//! Init command implementation for project initialization
//! 
//! This module handles creating new Node.js projects with various templates.

use crate::types::PackageJson;
use crate::ui;
use anyhow::{Result, Context};
use colored::*;
use std::collections::HashMap;
use std::io::{self, Write};
use std::path::Path;
use std::time::Instant;
use tokio::fs;

pub async fn handle_init(cmd: crate::InitCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    let project_name = cmd.name.unwrap_or_else(|| {
        std::env::current_dir()
            .ok()
            .and_then(|path| path.file_name().map(|name| name.to_string_lossy().to_string()))
            .unwrap_or_else(|| "my-project".to_string())
    });
    
    if verbose {
        println!("{} Initializing project '{}'...", 
            "info".cyan().bold(),
            project_name.bright_white()
        );
    }
    
    // Check if package.json already exists
    let package_json_path = Path::new("package.json");
    if package_json_path.exists() && !cmd.yes {
        print!("package.json already exists. Overwrite? (y/N): ");
        io::stdout().flush()?;
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        
        if !input.trim().to_lowercase().starts_with('y') {
            ui::show_info("Init cancelled");
            return Ok(());
        }
    }
    
    // Gather project information
    let project_info = if cmd.yes {
        ProjectInfo::default_with_name(project_name, cmd.typescript)
    } else {
        gather_project_info(project_name, cmd.typescript).await?
    };
    
    // Create package.json
    create_package_json(&project_info).await?;
    
    // Create additional files based on template
    create_template_files(&project_info, verbose).await?;
    
    // Create .gitignore
    create_gitignore().await?;
    
    // Create README.md
    create_readme(&project_info).await?;
    
    let elapsed = start_time.elapsed();
    
    ui::show_success(&format!(
        "Successfully initialized project '{}' in {:.2}s",
        project_info.name,
        elapsed.as_secs_f64()
    ));
    
    // Show next steps
    show_next_steps(&project_info);
    
    Ok(())
}

#[derive(Debug, Clone)]
struct ProjectInfo {
    name: String,
    version: String,
    description: String,
    author: String,
    license: String,
    entry_point: String,
    test_command: String,
    repository: String,
    keywords: Vec<String>,
    typescript: bool,
}

impl ProjectInfo {
    fn default_with_name(name: String, typescript: bool) -> Self {
        Self {
            name,
            version: "1.0.0".to_string(),
            description: String::new(),
            author: String::new(),
            license: "MIT".to_string(),
            entry_point: if typescript { "src/index.ts" } else { "index.js" }.to_string(),
            test_command: "echo \"Error: no test specified\" && exit 1".to_string(),
            repository: String::new(),
            keywords: Vec::new(),
            typescript,
        }
    }
}

async fn gather_project_info(name: String, typescript: bool) -> Result<ProjectInfo> {
    println!();
    println!("{}", "Project Setup".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    
    let mut info = ProjectInfo::default_with_name(name, typescript);
    
    // Project name
    info.name = prompt_with_default("Project name", &info.name)?;
    
    // Version
    info.version = prompt_with_default("Version", &info.version)?;
    
    // Description
    info.description = prompt_optional("Description")?;
    
    // Entry point
    let default_entry = if typescript { "src/index.ts" } else { "index.js" };
    info.entry_point = prompt_with_default("Entry point", default_entry)?;
    
    // Test command
    info.test_command = prompt_with_default("Test command", &info.test_command)?;
    
    // Repository
    info.repository = prompt_optional("Git repository")?;
    
    // Keywords
    let keywords_input = prompt_optional("Keywords (comma-separated)")?;
    if !keywords_input.is_empty() {
        info.keywords = keywords_input
            .split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();
    }
    
    // Author
    info.author = prompt_optional("Author")?;
    
    // License
    info.license = prompt_with_default("License", &info.license)?;
    
    Ok(info)
}

fn prompt_with_default(prompt: &str, default: &str) -> Result<String> {
    print!("{}: ({}) ", prompt, default.dimmed());
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();
    
    if input.is_empty() {
        Ok(default.to_string())
    } else {
        Ok(input.to_string())
    }
}

fn prompt_optional(prompt: &str) -> Result<String> {
    print!("{}: ", prompt);
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    Ok(input.trim().to_string())
}

async fn create_package_json(info: &ProjectInfo) -> Result<()> {
    let mut scripts = HashMap::new();
    
    if info.typescript {
        scripts.insert("build".to_string(), "tsc".to_string());
        scripts.insert("start".to_string(), "node dist/index.js".to_string());
        scripts.insert("dev".to_string(), "ts-node src/index.ts".to_string());
        scripts.insert("watch".to_string(), "tsc --watch".to_string());
    } else {
        scripts.insert("start".to_string(), format!("node {}", info.entry_point));
        scripts.insert("dev".to_string(), format!("nodemon {}", info.entry_point));
    }
    
    scripts.insert("test".to_string(), info.test_command.clone());
    
    let package_json = PackageJson {
        name: Some(info.name.clone()),
        version: Some(info.version.clone()),
        description: if info.description.is_empty() { None } else { Some(info.description.clone()) },
        main: Some(if info.typescript { "dist/index.js".to_string() } else { info.entry_point.clone() }),
        scripts: Some(scripts),
        dependencies: Some(HashMap::new()),
        dev_dependencies: Some(if info.typescript {
            let mut dev_deps = HashMap::new();
            dev_deps.insert("typescript".to_string(), "^5.0.0".to_string());
            dev_deps.insert("@types/node".to_string(), "^20.0.0".to_string());
            dev_deps.insert("ts-node".to_string(), "^10.9.0".to_string());
            dev_deps
        } else {
            HashMap::new()
        }),
        peer_dependencies: None,
        optional_dependencies: None,
        keywords: if info.keywords.is_empty() { None } else { Some(info.keywords.clone()) },
        author: if info.author.is_empty() { None } else { Some(info.author.clone()) },
        license: Some(info.license.clone()),
        homepage: None,
        repository: if info.repository.is_empty() { 
            None 
        } else { 
            Some(crate::types::Repository {
                repo_type: Some("git".to_string()),
                url: Some(info.repository.clone()),
            })
        },
        bugs: None,
        engines: Some({
            let mut engines = HashMap::new();
            engines.insert("node".to_string(), ">=18.0.0".to_string());
            engines
        }),
        bin: None,
    };
    
    let json_content = serde_json::to_string_pretty(&package_json)
        .context("Failed to serialize package.json")?;
    
    fs::write("package.json", json_content).await
        .context("Failed to write package.json")?;
    
    println!("{} Created package.json", "✓".bright_green());
    
    Ok(())
}

async fn create_template_files(info: &ProjectInfo, verbose: bool) -> Result<()> {
    if info.typescript {
        create_typescript_files(info, verbose).await?;
    } else {
        create_javascript_files(info, verbose).await?;
    }
    
    Ok(())
}

async fn create_typescript_files(info: &ProjectInfo, verbose: bool) -> Result<()> {
    // Create src directory
    fs::create_dir_all("src").await?;
    
    // Create tsconfig.json
    let tsconfig = r#"{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}"#;
    
    fs::write("tsconfig.json", tsconfig).await?;
    if verbose {
        println!("{} Created tsconfig.json", "✓".bright_green());
    }
    
    // Create main TypeScript file
    let main_content = format!(r#"#!/usr/bin/env node

/**
 * {} - {}
 */

function main(): void {{
    console.log('Hello from {}!');
}}

if (require.main === module) {{
    main();
}}

export {{ main }};
"#, info.name, info.description, info.name);
    
    fs::write("src/index.ts", main_content).await?;
    if verbose {
        println!("{} Created src/index.ts", "✓".bright_green());
    }
    
    Ok(())
}

async fn create_javascript_files(info: &ProjectInfo, verbose: bool) -> Result<()> {
    let main_content = format!(r#"#!/usr/bin/env node

/**
 * {} - {}
 */

function main() {{
    console.log('Hello from {}!');
}}

if (require.main === module) {{
    main();
}}

module.exports = {{ main }};
"#, info.name, info.description, info.name);
    
    fs::write(&info.entry_point, main_content).await?;
    if verbose {
        println!("{} Created {}", "✓".bright_green(), info.entry_point);
    }
    
    Ok(())
}

async fn create_gitignore() -> Result<()> {
    let gitignore_content = r#"# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Build output
dist/
build/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
"#;
    
    fs::write(".gitignore", gitignore_content).await?;
    println!("{} Created .gitignore", "✓".bright_green());
    
    Ok(())
}

async fn create_readme(info: &ProjectInfo) -> Result<()> {
    let readme_content = format!(r#"# {}

{}

## Installation

```bash
nx install
```

## Usage

```bash
{}
```

## Development

```bash
{}
```

## Scripts

- `nx run start` - Start the application
- `nx run dev` - Start in development mode{}
- `nx run test` - Run tests

## License

{}
"#, 
    info.name,
    if info.description.is_empty() { 
        format!("A Node.js project created with NX package manager.")
    } else { 
        info.description.clone() 
    },
    if info.typescript { "nx run start" } else { "nx run start" },
    if info.typescript { "nx run dev" } else { "nx run dev" },
    if info.typescript { "\n- `nx run build` - Build TypeScript to JavaScript\n- `nx run watch` - Watch for changes and rebuild" } else { "" },
    info.license
);
    
    fs::write("README.md", readme_content).await?;
    println!("{} Created README.md", "✓".bright_green());
    
    Ok(())
}

fn show_next_steps(info: &ProjectInfo) {
    println!();
    println!("{}", "🚀 Next steps:".bright_cyan().bold());
    println!();
    
    if info.typescript {
        println!("  {} Install TypeScript dependencies:", "1.".bright_white().bold());
        println!("     {} {} {}", "nx".bright_cyan(), "install".bright_green(), "@types/node typescript ts-node".bright_white());
        println!();
        println!("  {} Build the project:", "2.".bright_white().bold());
        println!("     {} {} {}", "nx".bright_cyan(), "run".bright_green(), "build".bright_white());
        println!();
        println!("  {} Start development:", "3.".bright_white().bold());
        println!("     {} {} {}", "nx".bright_cyan(), "run".bright_green(), "dev".bright_white());
    } else {
        println!("  {} Install dependencies:", "1.".bright_white().bold());
        println!("     {} {}", "nx".bright_cyan(), "install".bright_green());
        println!();
        println!("  {} Start the application:", "2.".bright_white().bold());
        println!("     {} {} {}", "nx".bright_cyan(), "run".bright_green(), "start".bright_white());
    }
    
    println!();
    println!("  {} Initialize git repository:", "🔧".bright_yellow());
    println!("     {} && {} && {}", 
        "git init".bright_white(),
        "git add .".bright_white(),
        "git commit -m \"Initial commit\"".bright_white()
    );
    
    println!();
}
