{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 5226164553319255144, "path": 902712116728367258, "deps": [[5103565458935487, "futures_io", false, 57889758856659197], [1811549171721445101, "futures_channel", false, 7412032595939094609], [7013762810557009322, "futures_sink", false, 9521482104851234385], [7620660491849607393, "futures_core", false, 8792809557733716970], [10629569228670356391, "futures_util", false, 12431046526132751751], [12779779637805422465, "futures_executor", false, 17836970641734506283], [16240732885093539806, "futures_task", false, 9258001406010940279]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-f162def788bcc788\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}