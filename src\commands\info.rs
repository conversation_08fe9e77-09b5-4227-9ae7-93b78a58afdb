//! Info command implementation with detailed package information
//! 
//! This module handles displaying detailed information about packages.

use crate::resolver::DependencyResolver;
use crate::ui;
use anyhow::Result;

pub async fn handle_info(package: String, versions: bool) -> Result<()> {
    let mut resolver = DependencyResolver::new()?;
    
    ui::show_spinner(&format!("Fetching info for {}...", package));
    
    let package_info = resolver.get_package_info(&package).await?;
    
    println!();
    
    // Calculate box width based on content
    let title = &package_info.name;
    let version_line = format!("Version:        {}", package_info.version);
    let desc_line = format!("Description:    {}", 
        package_info.description.as_deref().unwrap_or("No description"));
    let license_line = format!("License:        {}", 
        package_info.license.as_deref().unwrap_or("Unknown"));
    let deps_line = format!("Dependencies:   {}", package_info.dependencies.len());
    
    let max_width = [title, &version_line, &desc_line, &license_line, &deps_line]
        .iter()
        .map(|s| s.len())
        .max()
        .unwrap_or(50)
        .max(50);
    
    // Display package information in a box
    println!("┌{}┐", "─".repeat(max_width + 2));
    println!("│ {:<width$} │", title, width = max_width);
    println!("├{}┤", "─".repeat(max_width + 2));
    println!("│ {:<width$} │", version_line, width = max_width);
    println!("│ {:<width$} │", desc_line, width = max_width);
    println!("│ {:<width$} │", license_line, width = max_width);
    println!("│ {:<width$} │", deps_line, width = max_width);
    
    if versions && !package_info.versions.is_empty() {
        println!("├{}┤", "─".repeat(max_width + 2));
        println!("│ {:<width$} │", "Available versions:", width = max_width);
        
        let latest_versions: Vec<_> = package_info.versions
            .iter()
            .rev()
            .take(10)
            .collect();
        
        for version in latest_versions {
            let version_line = format!("  • {}", version);
            println!("│ {:<width$} │", version_line, width = max_width);
        }
        
        if package_info.versions.len() > 10 {
            let more_line = format!("  ... and {} more", package_info.versions.len() - 10);
            println!("│ {:<width$} │", more_line, width = max_width);
        }
    }
    
    println!("└{}┘", "─".repeat(max_width + 2));
    
    Ok(())
}
