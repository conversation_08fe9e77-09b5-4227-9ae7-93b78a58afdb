{"rustc": 1842507548689473721, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 10809724437792986082, "path": 2592930079643880870, "deps": [[41016358116313498, "hyper_util", false, 2116361931397317878], [784494742817713399, "tower_service", false, 14798241922623160777], [1542112352204983347, "rustls", false, 3703607446898342841], [2883436298747778685, "pki_types", false, 17783391813219956529], [8153991275959898788, "webpki_roots", false, 767445256429290472], [9010263965687315507, "http", false, 16923173100736676894], [11895591994124935963, "tokio_rustls", false, 2499223620782852583], [11957360342995674422, "hyper", false, 4818245367151501077], [17531218394775549125, "tokio", false, 15127316769836751075]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-4c328e662c8e1a46\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}