{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 11876527447619405325, "path": 16034447377844208515, "deps": [[2828590642173593838, "cfg_if", false, 17210828561417997836], [5491919304041016563, "build_script_build", false, 8803369370966081159], [8995469080876806959, "untrusted", false, 15096358293707274986], [9920160576179037441, "getrandom", false, 11958070509783973618]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-6c135747d8e4dd77\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}