//! Exec command implementation for running package binaries
//! 
//! This module handles executing installed package binaries.

use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::env;
use std::path::{Path, PathBuf};
use std::process::Stdio;
use std::time::Instant;
use tokio::process::Command;

pub async fn handle_exec(cmd: crate::ExecCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if verbose {
        println!("{} Executing '{}'...", 
            "info".cyan().bold(),
            format!("{} {}", cmd.command, cmd.args.join(" ")).bright_white()
        );
    }
    
    // Find the executable
    let executable_path = find_executable(&cmd.command)?;
    
    if verbose {
        println!("{} Found executable: {}", 
            "info".cyan(),
            executable_path.display().to_string().bright_white()
        );
    }
    
    // Execute the command
    let exit_status = execute_command(&executable_path, &cmd.args, verbose).await?;
    
    let elapsed = start_time.elapsed();
    
    if exit_status.success() {
        if verbose {
            ui::show_success(&format!(
                "Command completed successfully in {:.2}s",
                elapsed.as_secs_f64()
            ));
        }
    } else {
        let code = exit_status.code().unwrap_or(-1);
        if verbose {
            ui::show_error(&format!(
                "Command failed with exit code {} after {:.2}s",
                code,
                elapsed.as_secs_f64()
            ));
        }
        std::process::exit(code);
    }
    
    Ok(())
}

fn find_executable(command: &str) -> Result<PathBuf> {
    // First, check local node_modules/.bin
    let local_bin_path = Path::new("node_modules/.bin").join(command);
    if local_bin_path.exists() {
        return Ok(local_bin_path);
    }
    
    // Add .cmd extension on Windows
    if cfg!(target_os = "windows") {
        let local_bin_cmd_path = Path::new("node_modules/.bin").join(format!("{}.cmd", command));
        if local_bin_cmd_path.exists() {
            return Ok(local_bin_cmd_path);
        }
    }
    
    // Check global packages bin directory
    if let Ok(global_dir) = utils::get_global_packages_dir() {
        let global_bin_path = global_dir.join("bin").join(command);
        if global_bin_path.exists() {
            return Ok(global_bin_path);
        }
        
        // Add .cmd extension on Windows for global binaries
        if cfg!(target_os = "windows") {
            let global_bin_cmd_path = global_dir.join("bin").join(format!("{}.cmd", command));
            if global_bin_cmd_path.exists() {
                return Ok(global_bin_cmd_path);
            }
        }
    }
    
    // Check system PATH
    if let Some(path) = find_in_path(command) {
        return Ok(path);
    }
    
    // If not found, return error with helpful message
    Err(anyhow::anyhow!(
        "Command '{}' not found. Make sure it's installed or available in PATH", 
        command
    ))
}

fn find_in_path(command: &str) -> Option<PathBuf> {
    if let Ok(path_env) = env::var("PATH") {
        let paths = env::split_paths(&path_env);
        
        for path in paths {
            let executable = path.join(command);
            if executable.exists() {
                return Some(executable);
            }
            
            // Try with .exe extension on Windows
            if cfg!(target_os = "windows") {
                let executable_exe = path.join(format!("{}.exe", command));
                if executable_exe.exists() {
                    return Some(executable_exe);
                }
                
                let executable_cmd = path.join(format!("{}.cmd", command));
                if executable_cmd.exists() {
                    return Some(executable_cmd);
                }
                
                let executable_bat = path.join(format!("{}.bat", command));
                if executable_bat.exists() {
                    return Some(executable_bat);
                }
            }
        }
    }
    
    None
}

async fn execute_command(
    executable_path: &Path,
    args: &[String],
    verbose: bool,
) -> Result<std::process::ExitStatus> {
    if verbose {
        println!("{} Executing: {} {}", 
            "info".cyan(),
            executable_path.display().to_string().bright_white(),
            args.join(" ").bright_white()
        );
        println!("{}", "─".repeat(80).dimmed());
    }
    
    let status = Command::new(executable_path)
        .args(args)
        .stdin(Stdio::inherit())
        .stdout(Stdio::inherit())
        .stderr(Stdio::inherit())
        .status()
        .await
        .context("Failed to execute command")?;
    
    Ok(status)
}

// Helper function to list available executables
pub async fn list_executables(verbose: bool) -> Result<()> {
    println!();
    println!("{}", "📦 Available Executables".bright_cyan().bold());
    println!("{}", "─".repeat(50).dimmed());
    
    let mut executables = Vec::new();
    
    // Scan local node_modules/.bin
    let local_bin_dir = Path::new("node_modules/.bin");
    if local_bin_dir.exists() {
        if let Ok(entries) = tokio::fs::read_dir(local_bin_dir).await {
            let mut entries = entries;
            
            while let Ok(Some(entry)) = entries.next_entry().await {
                let name = entry.file_name().to_string_lossy().to_string();
                
                // Skip non-executable files and Windows-specific files
                if name.ends_with(".cmd") || name.ends_with(".ps1") {
                    continue;
                }
                
                executables.push((name, "local".to_string()));
            }
        }
    }
    
    // Scan global bin directory
    if let Ok(global_dir) = utils::get_global_packages_dir() {
        let global_bin_dir = global_dir.join("bin");
        if global_bin_dir.exists() {
            if let Ok(entries) = tokio::fs::read_dir(global_bin_dir).await {
                let mut entries = entries;
                
                while let Ok(Some(entry)) = entries.next_entry().await {
                    let name = entry.file_name().to_string_lossy().to_string();
                    
                    // Skip non-executable files and Windows-specific files
                    if name.ends_with(".cmd") || name.ends_with(".ps1") {
                        continue;
                    }
                    
                    // Avoid duplicates
                    if !executables.iter().any(|(n, _)| n == &name) {
                        executables.push((name, "global".to_string()));
                    }
                }
            }
        }
    }
    
    if executables.is_empty() {
        ui::show_info("No package executables found");
        println!();
        println!("{}", "💡 To install packages with executables:".bright_cyan());
        println!("  {} {} {}", "nx".bright_cyan(), "install".bright_green(), "<package-name>".bright_white());
        return Ok(());
    }
    
    // Sort executables by name
    executables.sort_by(|a, b| a.0.cmp(&b.0));
    
    println!();
    println!("{}", "Local Executables:".bright_white().bold());
    
    let local_executables: Vec<_> = executables.iter()
        .filter(|(_, scope)| scope == "local")
        .collect();
    
    if local_executables.is_empty() {
        println!("  {}", "None found".dimmed());
    } else {
        for (name, _) in local_executables {
            println!("  {} {}", 
                name.bright_green(),
                format!("nx exec {}", name).dimmed()
            );
        }
    }
    
    println!();
    println!("{}", "Global Executables:".bright_white().bold());
    
    let global_executables: Vec<_> = executables.iter()
        .filter(|(_, scope)| scope == "global")
        .collect();
    
    if global_executables.is_empty() {
        println!("  {}", "None found".dimmed());
    } else {
        for (name, _) in global_executables {
            println!("  {} {}", 
                name.bright_blue(),
                format!("nx exec {}", name).dimmed()
            );
        }
    }
    
    println!();
    println!("{}", "💡 Usage Examples:".bright_cyan().bold());
    println!("  {} {} {}", "nx".bright_cyan(), "exec".bright_green(), "tsc --version".bright_white());
    println!("  {} {} {}", "nx".bright_cyan(), "exec".bright_green(), "eslint src/".bright_white());
    println!("  {} {} {}", "nx".bright_cyan(), "exec".bright_green(), "prettier --write .".bright_white());
    
    println!();
    
    Ok(())
}

// Helper function to check if a command exists
pub fn command_exists(command: &str) -> bool {
    find_executable(command).is_ok()
}

// Helper function to get command path
pub fn get_command_path(command: &str) -> Option<PathBuf> {
    find_executable(command).ok()
}
