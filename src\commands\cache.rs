//! Cache command implementation

use crate::cache::PackageCache;
use crate::ui;
use anyhow::Result;

pub async fn handle_cache_clear() -> Result<()> {
    ui::show_info("🧹 Clearing cache...");
    let cache = PackageCache::new()?;
    cache.clear()?;
    ui::show_success("Cache cleared successfully!");
    Ok(())
}

pub async fn handle_cache_stats() -> Result<()> {
    ui::show_info("📊 Calculating cache statistics...");
    
    let cache = PackageCache::new()?;
    let stats = cache.get_stats()?;
    
    println!();
    ui::show_info("📊 Cache Statistics:");
    println!("┌────────────────────────────────────────┐");
    println!("│ {:<38} │", format!("Location: {}", stats.cache_dir.display()));
    println!("│ {:<38} │", format!("Total files: {}", stats.total_files));
    println!("│ {:<38} │", format!("Cached packages: {}", stats.total_files));
    println!("│ {:<38} │", format!("Total size: {}", crate::ui::format_bytes(stats.total_size)));
    println!("└────────────────────────────────────────┘");
    
    Ok(())
}

pub async fn handle_cache_verify() -> Result<()> {
    ui::show_info("🔍 Verifying cache integrity...");
    
    let cache = PackageCache::new()?;
    let result = cache.verify()?;
    
    ui::show_success(&format!(
        "Verified {} files, removed {} corrupt files",
        result.verified_files,
        result.corrupt_files_removed
    ));
    
    Ok(())
}
