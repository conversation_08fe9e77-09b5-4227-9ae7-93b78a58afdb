//! Publish command implementation for package publishing
//! 
//! This module handles publishing packages to npm registry.

use crate::commands::config::get_auth_token;
use crate::commands::login::is_logged_in;
use crate::types::PackageJson;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::path::Path;
use std::time::Instant;

pub async fn handle_publish(cmd: crate::PublishCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    let package_path = cmd.path.as_deref().unwrap_or(".");
    let package_path = Path::new(package_path);
    
    if verbose {
        println!("{} Publishing package from '{}'...", 
            "info".cyan().bold(),
            package_path.display().to_string().bright_white()
        );
    }
    
    // Check if logged in
    if !is_logged_in(None).await {
        ui::show_error("Not logged in. Run 'nx login' first.");
        return Ok(());
    }
    
    // Read and validate package.json
    let package_json_path = package_path.join("package.json");
    if !package_json_path.exists() {
        ui::show_error("No package.json found in the specified directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(package_path).await?
        .context("Failed to parse package.json")?;
    
    // Validate package for publishing
    validate_package(&package_json)?;
    
    if cmd.dry_run {
        perform_dry_run(&package_json, &cmd, verbose).await?;
    } else {
        perform_publish(&package_json, &cmd, verbose).await?;
    }
    
    let elapsed = start_time.elapsed();
    
    if cmd.dry_run {
        ui::show_success(&format!(
            "Dry run completed in {:.2}s",
            elapsed.as_secs_f64()
        ));
    } else {
        ui::show_success(&format!(
            "Package published successfully in {:.2}s",
            elapsed.as_secs_f64()
        ));
    }
    
    Ok(())
}

fn validate_package(package_json: &PackageJson) -> Result<()> {
    if package_json.name.is_none() {
        return Err(anyhow::anyhow!("Package name is required in package.json"));
    }
    
    if package_json.version.is_none() {
        return Err(anyhow::anyhow!("Package version is required in package.json"));
    }
    
    let name = package_json.name.as_ref().unwrap();
    
    // Validate package name
    if name.is_empty() || name.len() > 214 {
        return Err(anyhow::anyhow!("Invalid package name length"));
    }
    
    if name.starts_with('.') || name.starts_with('_') {
        return Err(anyhow::anyhow!("Package name cannot start with '.' or '_'"));
    }
    
    // Check for required fields
    if package_json.description.is_none() {
        ui::show_warning("Package description is missing. Consider adding one.");
    }
    
    if package_json.license.is_none() {
        ui::show_warning("Package license is missing. Consider adding one.");
    }
    
    if package_json.repository.is_none() {
        ui::show_warning("Package repository is missing. Consider adding one.");
    }
    
    Ok(())
}

async fn perform_dry_run(
    package_json: &PackageJson,
    cmd: &crate::PublishCommand,
    verbose: bool,
) -> Result<()> {
    println!();
    println!("{}", "📦 Publish Dry Run".bright_cyan().bold());
    println!("{}", "─".repeat(50).dimmed());
    
    let name = package_json.name.as_ref().unwrap();
    let version = package_json.version.as_ref().unwrap();
    
    println!("  {} {}", "Package:".dimmed(), name.bright_white());
    println!("  {} {}", "Version:".dimmed(), version.bright_blue());
    
    if let Some(ref description) = package_json.description {
        println!("  {} {}", "Description:".dimmed(), description.white());
    }
    
    if let Some(ref license) = package_json.license {
        println!("  {} {}", "License:".dimmed(), license.bright_white());
    }
    
    if let Some(ref tag) = cmd.tag {
        println!("  {} {}", "Tag:".dimmed(), tag.bright_yellow());
    } else {
        println!("  {} {}", "Tag:".dimmed(), "latest".bright_yellow());
    }
    
    println!();
    println!("{}", "📋 Files to be published:".bright_cyan());
    
    // List files that would be published
    let files_to_publish = get_publish_files().await?;
    for file in &files_to_publish {
        println!("  {} {}", "•".dimmed(), file.bright_white());
    }
    
    if files_to_publish.is_empty() {
        ui::show_warning("No files found to publish. Check your .npmignore or package.json files field.");
    }
    
    println!();
    println!("{}", "✅ Package is ready for publishing".bright_green());
    println!("   {} Run without --dry-run to publish", "💡".bright_yellow());
    
    Ok(())
}

async fn perform_publish(
    package_json: &PackageJson,
    cmd: &crate::PublishCommand,
    verbose: bool,
) -> Result<()> {
    ui::show_info("Package publishing is not yet fully implemented");
    
    let name = package_json.name.as_ref().unwrap();
    let version = package_json.version.as_ref().unwrap();
    
    println!();
    println!("{}", "📦 Publishing Process".bright_cyan().bold());
    println!("{}", "─".repeat(50).dimmed());
    
    println!("  {} Building package tarball...", "1.".bright_white().bold());
    println!("  {} Uploading to registry...", "2.".bright_white().bold());
    println!("  {} Updating package metadata...", "3.".bright_white().bold());
    println!("  {} Tagging version...", "4.".bright_white().bold());
    
    println!();
    println!("{} Would publish:", "📋".bright_cyan());
    println!("  {} {} {}",
        "Package:".dimmed(),
        name.bright_white(),
        format!("v{}", version).bright_blue()
    );
    
    if let Some(ref tag) = cmd.tag {
        println!("  {} {}", "Tag:".dimmed(), tag.bright_yellow());
    }
    
    println!();
    
    Ok(())
}

async fn get_publish_files() -> Result<Vec<String>> {
    // This is a simplified implementation
    // In reality, you'd need to:
    // 1. Read .npmignore and .gitignore
    // 2. Check package.json files field
    // 3. Apply npm's default ignore rules
    // 4. Build the actual file list
    
    let common_files = vec![
        "package.json".to_string(),
        "README.md".to_string(),
        "LICENSE".to_string(),
        "index.js".to_string(),
        "lib/".to_string(),
        "dist/".to_string(),
    ];
    
    let mut files_to_publish = Vec::new();
    
    for file in common_files {
        let path = Path::new(&file);
        if path.exists() {
            files_to_publish.push(file);
        }
    }
    
    Ok(files_to_publish)
}

// Helper function to check if package exists in registry
pub async fn package_exists(name: &str, version: &str) -> Result<bool> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .build()?;
    
    let url = format!("https://registry.npmjs.org/{}/{}", 
        urlencoding::encode(name), 
        urlencoding::encode(version)
    );
    
    let response = client
        .get(&url)
        .header("User-Agent", "nx/0.2.0")
        .send()
        .await?;
    
    Ok(response.status().is_success())
}

// Helper function to get package versions
pub async fn get_package_versions(name: &str) -> Result<Vec<String>> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .build()?;
    
    let url = format!("https://registry.npmjs.org/{}", urlencoding::encode(name));
    
    let response = client
        .get(&url)
        .header("User-Agent", "nx/0.2.0")
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Ok(Vec::new());
    }
    
    let manifest: serde_json::Value = response.json().await?;
    
    if let Some(versions) = manifest.get("versions").and_then(|v| v.as_object()) {
        let mut version_list: Vec<String> = versions.keys().cloned().collect();
        version_list.sort();
        Ok(version_list)
    } else {
        Ok(Vec::new())
    }
}
