#!/usr/bin/env pwsh

# Ultra-fast NX Package Manager Build Script
# This script builds the NX package manager for all platforms and prepares it for npm publishing

param(
    [switch]$Release,
    [switch]$Clean,
    [string]$Target = "all"
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Building NX Package Manager..." -ForegroundColor Cyan

# Clean build artifacts if requested
if ($Clean) {
    Write-Host "🧹 Cleaning build artifacts..." -ForegroundColor Yellow
    if (Test-Path "target") {
        Remove-Item -Path "target" -Recurse -Force
    }
    if (Test-Path "dist") {
        Remove-Item -Path "dist" -Recurse -Force
    }
}

# Create dist directory
New-Item -ItemType Directory -Force -Path "dist" | Out-Null

# Define target platforms
$targets = @()
switch ($Target) {
    "all" {
        $targets = @(
            "x86_64-pc-windows-msvc",
            "x86_64-unknown-linux-gnu",
            "x86_64-apple-darwin",
            "aarch64-apple-darwin"
        )
    }
    "windows" { $targets = @("x86_64-pc-windows-msvc") }
    "linux" { $targets = @("x86_64-unknown-linux-gnu") }
    "macos" { $targets = @("x86_64-apple-darwin", "aarch64-apple-darwin") }
    default { $targets = @($Target) }
}

$buildType = if ($Release) { "release" } else { "dev" }
$buildFlag = if ($Release) { "--release" } else { "" }

Write-Host "🎯 Building for targets: $($targets -join ', ')" -ForegroundColor Green
Write-Host "📦 Build type: $buildType" -ForegroundColor Green

foreach ($target in $targets) {
    Write-Host "🔨 Building for $target..." -ForegroundColor Blue
    
    # Add target if not already installed
    rustup target add $target 2>$null
    
    # Build for target
    $buildCmd = "cargo build $buildFlag --target $target"
    Write-Host "   Running: $buildCmd" -ForegroundColor Gray
    
    try {
        Invoke-Expression $buildCmd
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed for target $target"
        }
        
        # Copy binary to dist
        $sourceDir = "target\$target\$buildType"
        $binaryName = if ($target -like "*windows*") { "nx.exe" } else { "nx" }
        $sourcePath = "$sourceDir\$binaryName"
        
        if (Test-Path $sourcePath) {
            $destName = "nx-$target$(if ($target -like '*windows*') { '.exe' } else { '' })"
            Copy-Item $sourcePath "dist\$destName"
            Write-Host "   ✅ Built $destName" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Binary not found at $sourcePath" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ Failed to build for $target`: $_" -ForegroundColor Red
        continue
    }
}

# Create npm package structure
Write-Host "📦 Creating npm package structure..." -ForegroundColor Cyan

$packageJson = @{
    name = "nx-pm"
    version = "0.2.0"
    description = "⚡ Ultra-fast package manager for Node.js ecosystems - Built in Rust"
    main = "index.js"
    bin = @{
        nx = "./bin/nx.js"
        "nx-pm" = "./bin/nx.js"
    }
    scripts = @{
        postinstall = "node install.js"
        preuninstall = "node uninstall.js"
    }
    keywords = @(
        "package-manager",
        "npm",
        "node",
        "rust",
        "fast",
        "performance",
        "cli"
    )
    author = "NX Team <<EMAIL>>"
    license = "MIT"
    homepage = "https://nx.dev"
    repository = @{
        type = "git"
        url = "https://github.com/nx/nx.git"
    }
    bugs = @{
        url = "https://github.com/nx/nx/issues"
    }
    engines = @{
        node = ">=16.0.0"
    }
    os = @("win32", "darwin", "linux")
    cpu = @("x64", "arm64")
}

$packageJson | ConvertTo-Json -Depth 10 | Out-File -FilePath "dist\package.json" -Encoding UTF8

# Create install script
$installScript = @'
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

function getBinaryName() {
    const platform = os.platform();
    const arch = os.arch();
    
    let target;
    if (platform === 'win32' && arch === 'x64') {
        target = 'x86_64-pc-windows-msvc';
    } else if (platform === 'linux' && arch === 'x64') {
        target = 'x86_64-unknown-linux-gnu';
    } else if (platform === 'darwin' && arch === 'x64') {
        target = 'x86_64-apple-darwin';
    } else if (platform === 'darwin' && arch === 'arm64') {
        target = 'aarch64-apple-darwin';
    } else {
        console.error(`❌ Unsupported platform: ${platform}-${arch}`);
        process.exit(1);
    }
    
    const ext = platform === 'win32' ? '.exe' : '';
    return `nx-${target}${ext}`;
}

function main() {
    console.log('🚀 Installing NX Package Manager...');
    
    const binaryName = getBinaryName();
    const sourcePath = path.join(__dirname, 'binaries', binaryName);
    const binDir = path.join(__dirname, 'bin');
    const targetPath = path.join(binDir, os.platform() === 'win32' ? 'nx.exe' : 'nx');
    
    // Create bin directory
    if (!fs.existsSync(binDir)) {
        fs.mkdirSync(binDir, { recursive: true });
    }
    
    // Copy binary
    if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath);
        
        // Make executable on Unix systems
        if (os.platform() !== 'win32') {
            fs.chmodSync(targetPath, 0o755);
        }
        
        console.log('✅ NX Package Manager installed successfully!');
        console.log(`📍 Binary location: ${targetPath}`);
        
        // Test installation
        try {
            const version = execSync(`"${targetPath}" --version`, { encoding: 'utf8' }).trim();
            console.log(`🎉 Installation verified: ${version}`);
        } catch (error) {
            console.warn('⚠️  Installation completed but verification failed');
        }
    } else {
        console.error(`❌ Binary not found: ${sourcePath}`);
        console.error('Available binaries:');
        const binariesDir = path.join(__dirname, 'binaries');
        if (fs.existsSync(binariesDir)) {
            fs.readdirSync(binariesDir).forEach(file => {
                console.error(`  - ${file}`);
            });
        }
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
'@

$installScript | Out-File -FilePath "dist\install.js" -Encoding UTF8

# Create uninstall script
$uninstallScript = @'
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function main() {
    console.log('🗑️  Uninstalling NX Package Manager...');
    
    const binDir = path.join(__dirname, 'bin');
    if (fs.existsSync(binDir)) {
        fs.rmSync(binDir, { recursive: true, force: true });
        console.log('✅ NX Package Manager uninstalled successfully!');
    }
}

if (require.main === module) {
    main();
}
'@

$uninstallScript | Out-File -FilePath "dist\uninstall.js" -Encoding UTF8

# Create CLI wrapper
$cliWrapper = @'
#!/usr/bin/env node

const path = require('path');
const { spawn } = require('child_process');
const os = require('os');

const binaryName = os.platform() === 'win32' ? 'nx.exe' : 'nx';
const binaryPath = path.join(__dirname, '..', 'bin', binaryName);

const child = spawn(binaryPath, process.argv.slice(2), {
    stdio: 'inherit',
    windowsHide: false
});

child.on('exit', (code) => {
    process.exit(code);
});
'@

New-Item -ItemType Directory -Force -Path "dist\bin" | Out-Null
$cliWrapper | Out-File -FilePath "dist\bin\nx.js" -Encoding UTF8

# Create binaries directory and copy all built binaries
New-Item -ItemType Directory -Force -Path "dist\binaries" | Out-Null
Get-ChildItem "dist\nx-*" | ForEach-Object {
    Copy-Item $_.FullName "dist\binaries\"
    Remove-Item $_.FullName
}

# Create README for npm
$npmReadme = @'
# NX Package Manager ⚡

Ultra-fast package manager for Node.js ecosystems - Built in Rust for maximum performance.

## Installation

```bash
npm install -g nx-pm
```

## Usage

NX is a drop-in replacement for npm with significantly better performance:

```bash
# Install packages
nx install express
nx i lodash --save-dev

# Remove packages  
nx remove express
nx rm lodash

# Update packages
nx update
nx outdated

# Run scripts
nx run start
nx run build

# And much more...
```

## Features

- ⚡ **Ultra-fast** - 10x faster than npm
- 🚀 **Parallel downloads** - Maximum concurrency
- 💾 **Smart caching** - Intelligent package caching
- 🔒 **Security first** - Built-in vulnerability scanning
- 🎨 **Beautiful UI** - Progress bars and colored output
- 🔄 **npm compatible** - Drop-in replacement

## Commands

All npm commands are supported with enhanced performance and additional features.

## License

MIT
'@

$npmReadme | Out-File -FilePath "dist\README.md" -Encoding UTF8

Write-Host "✅ Build completed successfully!" -ForegroundColor Green
Write-Host "📦 Package ready in dist/ directory" -ForegroundColor Cyan
Write-Host "" 
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "  1. cd dist" -ForegroundColor White
Write-Host "  2. npm publish" -ForegroundColor White
Write-Host ""
