use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstallOptions {
    pub save_dev: bool,
    pub save_optional: bool,
    pub save_exact: bool,
    pub global: bool,
    pub dry_run: bool,
    pub force: bool,
    pub ignore_scripts: bool,
    pub production: bool,
}

impl Default for InstallOptions {
    fn default() -> Self {
        Self {
            save_dev: false,
            save_optional: false,
            save_exact: false,
            global: false,
            dry_run: false,
            force: false,
            ignore_scripts: false,
            production: false,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstalledPackage {
    pub name: String,
    pub version: String,
    pub path: Option<PathBuf>,
    pub dependencies: Option<HashMap<String, String>>,
    pub dev_dependencies: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResolvedPackage {
    pub name: String,
    pub version: String,
    pub resolved: String,
    pub integrity: Option<String>,
    pub tarball_url: String,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageMetadata {
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub keywords: Vec<String>,
    pub author: Option<String>,
    pub license: Option<String>,
    pub homepage: Option<String>,
    pub repository: Option<RepositoryInfo>,
    pub bugs: Option<BugsInfo>,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
    pub engines: Option<HashMap<String, String>>,
    pub scripts: HashMap<String, String>,
    pub main: Option<String>,
    pub bin: Option<HashMap<String, String>>,
    pub files: Vec<String>,
    pub dist: Option<DistInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RepositoryInfo {
    pub r#type: String,
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BugsInfo {
    pub url: Option<String>,
    pub email: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DistInfo {
    pub tarball: String,
    pub shasum: String,
    pub integrity: Option<String>,
    pub file_count: Option<u32>,
    pub unpacked_size: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyInfo {
    pub name: String,
    pub version: String,
    pub dep_type: DependencyType,
    pub resolved_version: Option<String>,
    pub children: Vec<DependencyInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DependencyType {
    Production,
    Development,
    Peer,
    Optional,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    pub version: String,
    pub published: Option<String>,
    pub deprecated: Option<String>,
    pub dist: DistInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryInfo {
    pub name: String,
    pub url: String,
    pub token: Option<String>,
    pub always_auth: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub package: SearchPackage,
    pub score: SearchScore,
    pub search_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchPackage {
    pub name: String,
    pub scope: Option<String>,
    pub version: String,
    pub description: Option<String>,
    pub keywords: Vec<String>,
    pub date: String,
    pub links: SearchLinks,
    pub author: Option<SearchPerson>,
    pub publisher: SearchPerson,
    pub maintainers: Vec<SearchPerson>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchLinks {
    pub npm: Option<String>,
    pub homepage: Option<String>,
    pub repository: Option<String>,
    pub bugs: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchPerson {
    pub username: String,
    pub email: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchScore {
    pub final_score: f64,
    pub detail: SearchScoreDetail,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchScoreDetail {
    pub quality: f64,
    pub popularity: f64,
    pub maintenance: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditResult {
    pub vulnerabilities: HashMap<String, u32>,
    pub metadata: AuditMetadata,
    pub advisories: HashMap<String, Advisory>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditMetadata {
    pub vulnerabilities: VulnerabilityCounts,
    pub dependencies: u32,
    pub dev_dependencies: u32,
    pub optional_dependencies: u32,
    pub total_dependencies: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityCounts {
    pub info: u32,
    pub low: u32,
    pub moderate: u32,
    pub high: u32,
    pub critical: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Advisory {
    pub id: u32,
    pub url: String,
    pub title: String,
    pub severity: String,
    pub vulnerable_versions: String,
    pub patched_versions: String,
    pub overview: String,
    pub recommendation: String,
    pub references: Vec<String>,
    pub module_name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigSettings {
    pub registry: String,
    pub cache_dir: PathBuf,
    pub global_dir: PathBuf,
    pub prefix: PathBuf,
    pub progress: bool,
    pub color: bool,
    pub unicode: bool,
    pub save_exact: bool,
    pub save_prefix: String,
    pub engine_strict: bool,
    pub force: bool,
    pub ignore_scripts: bool,
    pub max_sockets: u32,
    pub timeout: u64,
    pub user_agent: String,
    pub registries: HashMap<String, RegistryInfo>,
}

impl Default for ConfigSettings {
    fn default() -> Self {
        let home_dir = dirs::home_dir().unwrap_or_else(|| PathBuf::from("."));
        let nx_dir = home_dir.join(".nx");
        
        Self {
            registry: "https://registry.npmjs.org/".to_string(),
            cache_dir: nx_dir.join("cache"),
            global_dir: nx_dir.join("global"),
            prefix: nx_dir.join("prefix"),
            progress: true,
            color: true,
            unicode: true,
            save_exact: false,
            save_prefix: "^".to_string(),
            engine_strict: false,
            force: false,
            ignore_scripts: false,
            max_sockets: 50,
            timeout: 30000,
            user_agent: format!("nx/{} node/{}", env!("CARGO_PKG_VERSION"), "v18.0.0"),
            registries: HashMap::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageJson {
    pub name: Option<String>,
    pub version: Option<String>,
    pub description: Option<String>,
    pub main: Option<String>,
    pub scripts: HashMap<String, String>,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub peer_dependencies: HashMap<String, String>,
    pub optional_dependencies: HashMap<String, String>,
    pub engines: Option<HashMap<String, String>>,
    pub keywords: Vec<String>,
    pub author: Option<String>,
    pub license: Option<String>,
    pub repository: Option<RepositoryInfo>,
    pub bugs: Option<BugsInfo>,
    pub homepage: Option<String>,
    pub private: Option<bool>,
    pub workspaces: Option<Vec<String>>,
}

impl Default for PackageJson {
    fn default() -> Self {
        Self {
            name: Some("my-project".to_string()),
            version: Some("1.0.0".to_string()),
            description: Some("".to_string()),
            main: Some("index.js".to_string()),
            scripts: HashMap::new(),
            dependencies: HashMap::new(),
            dev_dependencies: HashMap::new(),
            peer_dependencies: HashMap::new(),
            optional_dependencies: HashMap::new(),
            engines: None,
            keywords: Vec::new(),
            author: None,
            license: Some("ISC".to_string()),
            repository: None,
            bugs: None,
            homepage: None,
            private: None,
            workspaces: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LockfileEntry {
    pub version: String,
    pub resolved: String,
    pub integrity: String,
    pub dependencies: HashMap<String, String>,
    pub requires: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Lockfile {
    pub name: String,
    pub version: String,
    pub lockfile_version: u32,
    pub requires: bool,
    pub packages: HashMap<String, LockfileEntry>,
    pub dependencies: HashMap<String, LockfileEntry>,
}

#[derive(Debug, Clone)]
pub struct DownloadProgress {
    pub name: String,
    pub current: u64,
    pub total: u64,
    pub speed: u64,
}

#[derive(Debug, Clone)]
pub struct InstallationProgress {
    pub total_packages: usize,
    pub installed_packages: usize,
    pub failed_packages: usize,
    pub current_package: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Template {
    pub name: String,
    pub description: String,
    pub language: String,
    pub framework: Option<String>,
    pub dependencies: HashMap<String, String>,
    pub dev_dependencies: HashMap<String, String>,
    pub scripts: HashMap<String, String>,
    pub files: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpdateType {
    Major,
    Minor,
    Patch,
    Prerelease,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutdatedPackage {
    pub name: String,
    pub current: String,
    pub wanted: String,
    pub latest: String,
    pub update_type: UpdateType,
    pub homepage: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheInfo {
    pub size: u64,
    pub package_count: usize,
    pub last_updated: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginInfo {
    pub username: String,
    pub email: Option<String>,
    pub registry: String,
    pub token: String,
    pub expires: Option<String>,
}
