{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 11876527447619405325, "path": 2021742076165417178, "deps": [[9620753569207166497, "zerovec_derive", false, 16726439601167187793], [10706449961930108323, "yoke", false, 6144134537012854225], [17046516144589451410, "zerofrom", false, 3447728921592341932]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-ee04e9d04c669b7b\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}