//! Clean command implementation for cache and modules cleanup
//! 
//! This module handles cleaning cache directories and node_modules.

use crate::cache::PackageCache;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::path::Path;
use std::time::Instant;
use tokio::fs;
use walkdir::WalkDir;

pub async fn handle_clean(cmd: crate::CleanCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if cmd.all {
        clean_cache(verbose).await?;
        clean_node_modules(verbose).await?;
    } else if cmd.cache {
        clean_cache(verbose).await?;
    } else if cmd.modules {
        clean_node_modules(verbose).await?;
    } else {
        // Default: clean both
        clean_cache(verbose).await?;
        clean_node_modules(verbose).await?;
    }
    
    let elapsed = start_time.elapsed();
    ui::show_success(&format!(
        "Clean operation completed in {:.2}s",
        elapsed.as_secs_f64()
    ));
    
    Ok(())
}

async fn clean_cache(verbose: bool) -> Result<()> {
    if verbose {
        println!("{} Cleaning package cache...", "info".cyan().bold());
    }
    
    let cache = PackageCache::new()
        .context("Failed to initialize package cache")?;
    
    let cache_dir = cache.get_cache_dir();
    
    if !cache_dir.exists() {
        ui::show_info("Cache directory doesn't exist");
        return Ok(());
    }
    
    // Calculate cache size before cleaning
    let cache_size = calculate_directory_size(&cache_dir)?;
    
    if verbose {
        println!("{} Cache directory: {}", 
            "info".cyan(), cache_dir.display().to_string().bright_white());
        println!("{} Cache size: {}", 
            "info".cyan(), ui::format_bytes(cache_size).bright_white());
    }
    
    // Remove cache directory
    fs::remove_dir_all(&cache_dir).await
        .context("Failed to remove cache directory")?;
    
    // Recreate empty cache directory
    fs::create_dir_all(&cache_dir).await
        .context("Failed to recreate cache directory")?;
    
    println!("{} Cleaned cache (freed {})", 
        "✓".bright_green(), ui::format_bytes(cache_size).bright_green());
    
    Ok(())
}

async fn clean_node_modules(verbose: bool) -> Result<()> {
    if verbose {
        println!("{} Cleaning node_modules...", "info".cyan().bold());
    }
    
    let node_modules_path = Path::new("node_modules");
    
    if !node_modules_path.exists() {
        ui::show_info("No node_modules directory found");
        return Ok(());
    }
    
    // Calculate size before cleaning
    let modules_size = calculate_directory_size(node_modules_path)?;
    
    if verbose {
        println!("{} node_modules size: {}", 
            "info".cyan(), ui::format_bytes(modules_size).bright_white());
    }
    
    // Remove node_modules directory
    fs::remove_dir_all(node_modules_path).await
        .context("Failed to remove node_modules directory")?;
    
    println!("{} Cleaned node_modules (freed {})", 
        "✓".bright_green(), ui::format_bytes(modules_size).bright_green());
    
    // Also clean package-lock.json and yarn.lock if they exist
    clean_lockfiles(verbose).await?;
    
    Ok(())
}

async fn clean_lockfiles(verbose: bool) -> Result<()> {
    let lockfiles = ["package-lock.json", "yarn.lock", "nx-lock.json"];
    let mut cleaned_files = Vec::new();
    
    for lockfile in &lockfiles {
        let path = Path::new(lockfile);
        if path.exists() {
            fs::remove_file(path).await
                .with_context(|| format!("Failed to remove {}", lockfile))?;
            cleaned_files.push(*lockfile);
        }
    }
    
    if !cleaned_files.is_empty() && verbose {
        println!("{} Cleaned lockfiles: {}", 
            "✓".bright_green(), 
            cleaned_files.join(", ").bright_white()
        );
    }
    
    Ok(())
}

fn calculate_directory_size(dir: &Path) -> Result<u64> {
    let mut total_size = 0u64;
    
    for entry in WalkDir::new(dir).into_iter().filter_map(|e| e.ok()) {
        if entry.file_type().is_file() {
            if let Ok(metadata) = entry.metadata() {
                total_size += metadata.len();
            }
        }
    }
    
    Ok(total_size)
}

// Helper function to clean specific package cache
pub async fn clean_package_cache(package_name: &str, verbose: bool) -> Result<()> {
    let cache = PackageCache::new()
        .context("Failed to initialize package cache")?;
    
    let package_cache_dir = cache.get_cache_dir().join(package_name);
    
    if !package_cache_dir.exists() {
        if verbose {
            ui::show_info(&format!("No cache found for package '{}'", package_name));
        }
        return Ok(());
    }
    
    let cache_size = calculate_directory_size(&package_cache_dir)?;
    
    fs::remove_dir_all(&package_cache_dir).await
        .with_context(|| format!("Failed to remove cache for package '{}'", package_name))?;
    
    if verbose {
        println!("{} Cleaned cache for {} (freed {})", 
            "✓".bright_green(), 
            package_name.bright_white(),
            ui::format_bytes(cache_size).bright_green()
        );
    }
    
    Ok(())
}

// Helper function to get cache statistics
pub async fn get_cache_stats() -> Result<CacheStats> {
    let cache = PackageCache::new()
        .context("Failed to initialize package cache")?;
    
    let cache_dir = cache.get_cache_dir();
    
    if !cache_dir.exists() {
        return Ok(CacheStats {
            total_size: 0,
            package_count: 0,
            version_count: 0,
        });
    }
    
    let total_size = calculate_directory_size(&cache_dir)?;
    let mut package_count = 0;
    let mut version_count = 0;
    
    // Count packages and versions
    if let Ok(entries) = fs::read_dir(&cache_dir).await {
        let mut entries = entries;
        
        while let Ok(Some(entry)) = entries.next_entry().await {
            if entry.file_type().await?.is_dir() {
                package_count += 1;
                
                // Count versions for this package
                if let Ok(version_entries) = fs::read_dir(entry.path()).await {
                    let mut version_entries = version_entries;
                    
                    while let Ok(Some(_)) = version_entries.next_entry().await {
                        version_count += 1;
                    }
                }
            }
        }
    }
    
    Ok(CacheStats {
        total_size,
        package_count,
        version_count,
    })
}

#[derive(Debug)]
pub struct CacheStats {
    pub total_size: u64,
    pub package_count: u32,
    pub version_count: u32,
}

// Helper function to display cache information
pub async fn display_cache_info(verbose: bool) -> Result<()> {
    let stats = get_cache_stats().await?;
    
    println!();
    println!("{}", "📦 Cache Information".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    
    println!("  {} {}", "Total Size:".dimmed(), ui::format_bytes(stats.total_size).bright_white());
    println!("  {} {}", "Packages:".dimmed(), stats.package_count.to_string().bright_white());
    println!("  {} {}", "Versions:".dimmed(), stats.version_count.to_string().bright_white());
    
    let cache = PackageCache::new()
        .context("Failed to initialize package cache")?;
    let cache_dir = cache.get_cache_dir();
    
    println!("  {} {}", "Location:".dimmed(), cache_dir.display().to_string().bright_blue().underline());
    
    if verbose && stats.package_count > 0 {
        println!();
        println!("{}", "📋 Cached Packages:".bright_cyan().bold());
        
        if let Ok(entries) = fs::read_dir(&cache_dir).await {
            let mut entries = entries;
            let mut packages = Vec::new();
            
            while let Ok(Some(entry)) = entries.next_entry().await {
                if entry.file_type().await?.is_dir() {
                    let package_name = entry.file_name().to_string_lossy().to_string();
                    let package_size = calculate_directory_size(&entry.path())?;
                    packages.push((package_name, package_size));
                }
            }
            
            // Sort by size (largest first)
            packages.sort_by(|a, b| b.1.cmp(&a.1));
            
            // Show top 10 packages
            for (name, size) in packages.iter().take(10) {
                println!("  {} {}",
                    name.bright_white(),
                    ui::format_bytes(*size).dimmed()
                );
            }
            
            if packages.len() > 10 {
                println!("  {} ... and {} more packages",
                    "...".dimmed(),
                    (packages.len() - 10).to_string().dimmed()
                );
            }
        }
    }
    
    println!();
    
    Ok(())
}
