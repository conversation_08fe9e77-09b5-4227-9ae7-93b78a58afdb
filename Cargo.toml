[package]
name = "nx"
version = "0.2.0"
edition = "2021"
description = "Ultra-fast package manager for Node.js ecosystems - Built in Rust"
authors = ["NX Team <<EMAIL>>"]
license = "MIT"
homepage = "https://nx.dev"
repository = "https://github.com/nx/nx"
keywords = ["package-manager", "npm", "node", "rust", "fast"]
categories = ["command-line-utilities", "development-tools"]

[[bin]]
name = "nx"
path = "src/main.rs"

[dependencies]
# CLI Framework
clap = { version = "4.5", features = ["derive"] }

# Async Runtime
tokio = { version = "1.47", features = ["full"] }

# HTTP Client with connection pooling
reqwest = { version = "0.12", features = ["json", "rustls-tls", "stream"], default-features = false }

# JSON Processing
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Version handling
semver = { version = "1.0", features = ["serde"] }

# Terminal UI and Progress
colored = "2.1"
indicatif = { version = "0.17", features = ["rayon"] }

# Archive handling
tar = "0.4"
flate2 = "1.1"

# File system utilities
dirs = "5.0"
tempfile = "3.20"

# Parallel processing
rayon = "1.10"

# Cryptographic hashing
blake3 = "1.8"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Utilities
walkdir = "2.5"
pathdiff = "0.2"
futures-util = "0.3"
futures = "0.3"
atty = "0.2"
base64 = "0.22"
url = "2.5"
urlencoding = "2.1"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 1
debug = true