//! Audit command implementation for security vulnerability checking
//! 
//! This module handles security auditing of installed packages.

use crate::types::{AuditResult, AuditSummary, PackageJson, Severity, Vulnerability};
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use std::time::Instant;

#[derive(Debug, Deserialize)]
struct NpmAuditResponse {
    vulnerabilities: HashMap<String, NpmVulnerability>,
    metadata: AuditMetadata,
}

#[derive(Debug, Deserialize)]
struct NpmVulnerability {
    name: String,
    severity: String,
    via: Vec<VulnerabilityVia>,
    effects: Vec<String>,
    range: String,
    nodes: Vec<String>,
    #[serde(rename = "fixAvailable")]
    fix_available: Option<FixAvailable>,
}

#[derive(Debug, Deserialize)]
#[serde(untagged)]
enum VulnerabilityVia {
    String(String),
    Object(VulnerabilityInfo),
}

#[derive(Debug, Deserialize)]
struct VulnerabilityInfo {
    source: Option<u32>,
    name: String,
    dependency: String,
    title: String,
    url: String,
    severity: String,
    #[serde(rename = "cwe")]
    cwe: Option<Vec<String>>,
    #[serde(rename = "cvss")]
    cvss: Option<CvssInfo>,
    range: String,
}

#[derive(Debug, Deserialize)]
struct CvssInfo {
    score: f64,
    vectorString: String,
}

#[derive(Debug, Deserialize)]
#[serde(untagged)]
enum FixAvailable {
    Bool(bool),
    Object(FixInfo),
}

#[derive(Debug, Deserialize)]
struct FixInfo {
    name: String,
    version: String,
    #[serde(rename = "isSemVerMajor")]
    is_semver_major: bool,
}

#[derive(Debug, Deserialize)]
struct AuditMetadata {
    vulnerabilities: VulnerabilityCount,
    dependencies: u32,
    #[serde(rename = "devDependencies")]
    dev_dependencies: u32,
    #[serde(rename = "optionalDependencies")]
    optional_dependencies: u32,
    #[serde(rename = "totalDependencies")]
    total_dependencies: u32,
}

#[derive(Debug, Deserialize)]
struct VulnerabilityCount {
    info: u32,
    low: u32,
    moderate: u32,
    high: u32,
    critical: u32,
    total: u32,
}

pub async fn handle_audit(cmd: crate::AuditCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    // Check if package.json exists
    let package_json_path = Path::new("package.json");
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    if verbose {
        println!("{} Running security audit...", "info".cyan().bold());
    }
    
    let audit_result = perform_audit(&package_json, verbose).await?;
    
    let elapsed = start_time.elapsed();
    
    // Display audit results
    display_audit_results(&audit_result, cmd.detailed, elapsed);
    
    // Optionally fix vulnerabilities
    if cmd.fix {
        fix_vulnerabilities(&audit_result, verbose).await?;
    }
    
    // Exit with error code if vulnerabilities found
    if audit_result.summary.critical > 0 || audit_result.summary.high > 0 {
        std::process::exit(1);
    }
    
    Ok(())
}

async fn perform_audit(package_json: &PackageJson, verbose: bool) -> Result<AuditResult> {
    let client = Client::builder()
        .timeout(std::time::Duration::from_secs(60))
        .build()
        .context("Failed to create HTTP client")?;
    
    let spinner = ui::create_resolution_spinner();
    spinner.set_message("Auditing packages for vulnerabilities...");
    
    // Prepare audit request payload
    let audit_payload = create_audit_payload(package_json)?;
    
    let response = client
        .post("https://registry.npmjs.org/-/npm/v1/security/audits")
        .header("User-Agent", "nx/0.2.0")
        .header("Content-Type", "application/json")
        .json(&audit_payload)
        .send()
        .await
        .context("Failed to send audit request")?;
    
    spinner.finish_and_clear();
    
    if !response.status().is_success() {
        return Err(anyhow::anyhow!("Audit request failed: HTTP {}", response.status()));
    }
    
    let audit_response: NpmAuditResponse = response
        .json()
        .await
        .context("Failed to parse audit response")?;
    
    // Convert npm audit response to our format
    let vulnerabilities = convert_vulnerabilities(audit_response.vulnerabilities);
    let total_dependencies = audit_response.metadata.total_dependencies as usize;
    
    let summary = AuditSummary {
        critical: audit_response.metadata.vulnerabilities.critical as usize,
        high: audit_response.metadata.vulnerabilities.high as usize,
        moderate: audit_response.metadata.vulnerabilities.moderate as usize,
        low: audit_response.metadata.vulnerabilities.low as usize,
        info: audit_response.metadata.vulnerabilities.info as usize,
    };
    
    Ok(AuditResult {
        vulnerabilities,
        total_dependencies,
        summary,
    })
}

fn create_audit_payload(package_json: &PackageJson) -> Result<serde_json::Value> {
    let mut dependencies = HashMap::new();
    
    if let Some(ref deps) = package_json.dependencies {
        for (name, version) in deps {
            dependencies.insert(name.clone(), version.clone());
        }
    }
    
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        for (name, version) in dev_deps {
            dependencies.insert(name.clone(), version.clone());
        }
    }
    
    let payload = serde_json::json!({
        "name": package_json.name.as_deref().unwrap_or("unknown"),
        "version": package_json.version.as_deref().unwrap_or("1.0.0"),
        "requires": dependencies,
        "dependencies": {}
    });
    
    Ok(payload)
}

fn convert_vulnerabilities(npm_vulns: HashMap<String, NpmVulnerability>) -> Vec<Vulnerability> {
    let mut vulnerabilities = Vec::new();
    
    for (_, npm_vuln) in npm_vulns {
        for via in npm_vuln.via {
            if let VulnerabilityVia::Object(info) = via {
                let severity = match info.severity.as_str() {
                    "critical" => Severity::Critical,
                    "high" => Severity::High,
                    "moderate" => Severity::Moderate,
                    "low" => Severity::Low,
                    _ => Severity::Info,
                };
                
                let vulnerability = Vulnerability {
                    id: info.source.map(|s| s.to_string()).unwrap_or_else(|| "unknown".to_string()),
                    title: info.title,
                    severity,
                    vulnerable_versions: info.range,
                    patched_versions: None, // This would need to be extracted from fix info
                    overview: None,
                    recommendation: Some("Update to a patched version".to_string()),
                    cwe: info.cwe.map(|cwe_list| cwe_list.join(", ")),
                    cves: None,
                };
                
                vulnerabilities.push(vulnerability);
            }
        }
    }
    
    vulnerabilities
}

fn display_audit_results(result: &AuditResult, detailed: bool, elapsed: std::time::Duration) {
    println!();
    
    if result.summary.critical + result.summary.high + result.summary.moderate + result.summary.low + result.summary.info == 0 {
        ui::show_success(&format!(
            "✨ No vulnerabilities found in {} dependencies (scanned in {:.2}s)",
            result.total_dependencies,
            elapsed.as_secs_f64()
        ));
        return;
    }
    
    // Display summary
    println!("{}", "🔍 Security Audit Results".bright_red().bold());
    println!("{}", "─".repeat(50).dimmed());
    println!();
    
    let total_vulns = result.summary.critical + result.summary.high + result.summary.moderate + result.summary.low + result.summary.info;
    
    println!("{} {} vulnerabilities found in {} dependencies", 
        "⚠️".bright_red(),
        total_vulns.to_string().bright_red().bold(),
        result.total_dependencies.to_string().bright_white()
    );
    
    println!();
    
    // Severity breakdown
    if result.summary.critical > 0 {
        println!("  {} {} critical",
            "🔴".bright_red(),
            result.summary.critical.to_string().bright_red().bold()
        );
    }
    
    if result.summary.high > 0 {
        println!("  {} {} high",
            "🟠".bright_yellow(),
            result.summary.high.to_string().bright_red().bold()
        );
    }
    
    if result.summary.moderate > 0 {
        println!("  {} {} moderate",
            "🟡".bright_yellow(),
            result.summary.moderate.to_string().bright_yellow().bold()
        );
    }
    
    if result.summary.low > 0 {
        println!("  {} {} low",
            "🟢".bright_green(),
            result.summary.low.to_string().bright_white().bold()
        );
    }
    
    if result.summary.info > 0 {
        println!("  {} {} info",
            "ℹ️".bright_blue(),
            result.summary.info.to_string().bright_blue().bold()
        );
    }
    
    println!();
    
    // Detailed vulnerability information
    if detailed && !result.vulnerabilities.is_empty() {
        println!("{}", "📋 Vulnerability Details".bright_cyan().bold());
        println!("{}", "─".repeat(50).dimmed());
        println!();
        
        for (index, vuln) in result.vulnerabilities.iter().enumerate() {
            display_vulnerability_details(vuln, index + 1);
            
            if index < result.vulnerabilities.len() - 1 {
                println!("{}", "─".repeat(50).dimmed());
            }
        }
    }
    
    // Recommendations
    println!();
    println!("{}", "💡 Recommendations".bright_cyan().bold());
    println!("{}", "─".repeat(50).dimmed());
    
    if result.summary.critical > 0 || result.summary.high > 0 {
        println!("  {} Update vulnerable packages immediately", "1.".bright_white().bold());
        println!("     {} {} {}", "nx".bright_cyan(), "audit".bright_green(), "--fix".bright_white());
        println!();
    }
    
    println!("  {} Review and update your dependencies regularly", "2.".bright_white().bold());
    println!("     {} {} {}", "nx".bright_cyan(), "outdated".bright_green(), "# Check for updates".dimmed());
    println!();
    
    println!("  {} Consider using exact versions for critical dependencies", "3.".bright_white().bold());
    println!("     {} {} {}", "nx".bright_cyan(), "install".bright_green(), "--save-exact <package>".bright_white());
    
    println!();
    println!("{} Audit completed in {:.2}s", 
        "⏱️".bright_blue(),
        elapsed.as_secs_f64()
    );
}

fn display_vulnerability_details(vuln: &Vulnerability, index: usize) {
    let severity_color = match vuln.severity {
        Severity::Critical => "🔴 CRITICAL".bright_red().bold(),
        Severity::High => "🟠 HIGH".bright_red(),
        Severity::Moderate => "🟡 MODERATE".bright_yellow(),
        Severity::Low => "🟢 LOW".bright_green(),
        Severity::Info => "ℹ️ INFO".bright_blue(),
    };
    
    println!("{}. {} [{}]",
        index.to_string().dimmed(),
        vuln.title.bright_white().bold(),
        severity_color
    );
    
    if let Some(ref overview) = vuln.overview {
        println!("   {}", overview.white());
    }
    
    println!("   {} {}", "Vulnerable:".dimmed(), vuln.vulnerable_versions.bright_red());
    
    if let Some(ref patched) = vuln.patched_versions {
        println!("   {} {}", "Patched:".dimmed(), patched.bright_green());
    }
    
    if let Some(ref recommendation) = vuln.recommendation {
        println!("   {} {}", "Fix:".dimmed(), recommendation.bright_cyan());
    }
    
    if let Some(ref cwe) = vuln.cwe {
        println!("   {} {}", "CWE:".dimmed(), cwe.dimmed());
    }
    
    println!();
}

async fn fix_vulnerabilities(result: &AuditResult, verbose: bool) -> Result<()> {
    if result.vulnerabilities.is_empty() {
        return Ok(());
    }
    
    ui::show_info("Attempting to fix vulnerabilities...");
    
    // This is a simplified fix implementation
    // In a real-world scenario, you'd need to:
    // 1. Analyze the dependency tree
    // 2. Find compatible updates that fix vulnerabilities
    // 3. Update package.json with fixed versions
    // 4. Reinstall packages
    
    println!("{} Automatic vulnerability fixing is not yet implemented", 
        "⚠️".bright_yellow());
    println!("   Please manually update the affected packages:");
    println!();
    
    for vuln in &result.vulnerabilities {
        if let Some(ref recommendation) = vuln.recommendation {
            println!("  • {} {}", vuln.title.bright_white(), recommendation.dimmed());
        }
    }
    
    Ok(())
}
