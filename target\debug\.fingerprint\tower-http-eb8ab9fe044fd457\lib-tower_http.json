{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 11876527447619405325, "path": 16215392639322834235, "deps": [[784494742817713399, "tower_service", false, 3555923355747788413], [1906322745568073236, "pin_project_lite", false, 11564342335118793534], [4121350475192885151, "iri_string", false, 16581196288148533740], [5695049318159433696, "tower", false, 16366437721437854147], [7712452662827335977, "tower_layer", false, 7485140157545091005], [7896293946984509699, "bitflags", false, 9102162576752046998], [9010263965687315507, "http", false, 5292560152749097135], [10629569228670356391, "futures_util", false, 12431046526132751751], [14084095096285906100, "http_body", false, 13728545525073471822], [16066129441945555748, "bytes", false, 100208413366652360]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-eb8ab9fe044fd457\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}