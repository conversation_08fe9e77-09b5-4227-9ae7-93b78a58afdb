//! Utility functions for file operations, validation, and formatting
//! 
//! This module provides common utility functions used throughout the package manager.

use crate::errors::NxError;
use crate::types::PackageJson;
use anyhow::{Result, Context};
use dirs;
use std::path::{Path, PathBuf};
use std::time::Duration;
use tokio::fs;

/// Read and parse package.json from a directory (async version)
pub async fn read_package_json(dir: &Path) -> Result<Option<PackageJson>> {
    let package_json_path = dir.join("package.json");
    
    if !package_json_path.exists() {
        return Ok(None);
    }
    
    let content = fs::read_to_string(&package_json_path).await
        .context("Failed to read package.json")?;
    
    let package_json: PackageJson = serde_json::from_str(&content)
        .context("Failed to parse package.json")?;
    
    Ok(Some(package_json))
}

/// Get the global packages directory
pub fn get_global_packages_dir() -> Result<PathBuf> {
    let home_dir = dirs::home_dir()
        .context("Failed to get home directory")?;
    
    let global_dir = home_dir.join(".nx").join("global");
    
    // Ensure directory exists
    std::fs::create_dir_all(&global_dir)
        .context("Failed to create global packages directory")?;
    
    Ok(global_dir)
}

/// Write package.json to a directory (async version)
pub async fn write_package_json(path: &Path, package_json: &PackageJson) -> Result<()> {
    let content = serde_json::to_string_pretty(package_json)
        .context("Failed to serialize package.json")?;
    
    fs::write(path, content).await
        .context("Failed to write package.json")?;
    
    Ok(())
}

/// Check if a string is a valid package name
pub fn is_valid_package_name(name: &str) -> bool {
    if name.is_empty() || name.len() > 214 {
        return false;
    }
    
    // Must start with lowercase letter or number
    let first_char = name.chars().next().unwrap();
    if !first_char.is_ascii_lowercase() && !first_char.is_ascii_digit() {
        return false;
    }
    
    // Can only contain lowercase letters, digits, hyphens, underscores, and dots
    name.chars().all(|c| {
        c.is_ascii_lowercase() || c.is_ascii_digit() || c == '-' || c == '_' || c == '.'
    })
}

/// Extract package name from scoped or unscoped package names
pub fn extract_package_name(full_name: &str) -> &str {
    if full_name.starts_with('@') {
        // Scoped package like @types/node
        full_name
    } else {
        // Regular package
        full_name
    }
}

/// Format duration in human-readable format
pub fn format_duration(duration: Duration) -> String {
    let ms = duration.as_millis();
    
    if ms < 1000 {
        format!("{}ms", ms)
    } else if ms < 60_000 {
        format!("{:.1}s", ms as f64 / 1000.0)
    } else {
        let seconds = ms / 1000;
        let minutes = seconds / 60;
        let remaining_seconds = seconds % 60;
        format!("{}m {}s", minutes, remaining_seconds)
    }
}

/// Format bytes in human-readable format
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    if bytes == 0 {
        return "0 B".to_string();
    }
    
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Check if a path is safe (doesn't escape boundaries)
pub fn is_safe_path(path: &str) -> bool {
    !path.contains("..") && !path.starts_with('/')
}

/// Ensure a directory exists (async version)
pub async fn ensure_dir_exists(path: &Path) -> Result<()> {
    if !path.exists() {
        fs::create_dir_all(path).await
            .with_context(|| format!("Failed to create directory: {}", path.display()))?;
    }
    Ok(())
}

/// Get the total size of a directory
pub fn get_directory_size(path: &Path) -> Result<u64> {
    let mut total_size = 0;
    
    if path.is_dir() {
        for entry in walkdir::WalkDir::new(path) {
            let entry = entry?;
            if entry.file_type().is_file() {
                total_size += entry.metadata()?.len();
            }
        }
    }
    
    Ok(total_size)
}

/// Clean up empty directories
pub fn cleanup_empty_dirs(path: &Path) -> Result<()> {
    if !path.is_dir() {
        return Ok(());
    }
    
    for entry in fs::read_dir(path)? {
        let entry = entry?;
        let entry_path = entry.path();
        
        if entry_path.is_dir() {
            cleanup_empty_dirs(&entry_path)?;
            
            // Try to remove if empty
            if fs::read_dir(&entry_path)?.next().is_none() {
                let _ = fs::remove_dir(&entry_path);
            }
        }
    }
    
    Ok(())
}