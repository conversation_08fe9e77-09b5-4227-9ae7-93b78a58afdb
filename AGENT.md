# NX Package Manager - Agent Instructions

## Project Overview
Ultra-fast package manager for Node.js ecosystems built in Rust. Designed to be 10x faster than npm/pnpm/yarn/bun with beautiful UI, smart caching, and security-first approach.

## Commands

### Development
```bash
# Build for development
cargo build

# Build for release
cargo build --release

# Run with arguments
cargo run -- install express

# Run tests
cargo test

# Check code
cargo check

# Format code
cargo fmt

# Lint code
cargo clippy
```

### Cross-platform Building
```bash
# Build for all platforms (PowerShell)
.\scripts\build.ps1 -Release -Target all

# Build for specific platforms
.\scripts\build.ps1 -Release -Target windows
.\scripts\build.ps1 -Release -Target linux
.\scripts\build.ps1 -Release -Target macos
```

### Publishing to npm
```bash
# Publish to npm (after building)
.\scripts\publish.sh

# Or manually:
cd dist
npm publish --access public
```

## Project Structure
```
src/
├── main.rs              # CLI entry point with command parsing
├── commands/             # Command implementations
│   ├── mod.rs           # Command module exports
│   ├── install.rs       # Package installation
│   ├── remove.rs        # Package removal
│   ├── update.rs        # Package updates
│   ├── list.rs          # Package listing
│   ├── search.rs        # Package search
│   ├── show.rs          # Package information
│   ├── run.rs           # Script execution
│   ├── init.rs          # Project initialization
│   ├── clean.rs         # Cache/cleanup
│   ├── config.rs        # Configuration management
│   ├── audit.rs         # Security auditing
│   ├── publish.rs       # Package publishing
│   ├── login.rs         # Registry authentication
│   ├── logout.rs        # Registry logout
│   ├── link.rs          # Development linking
│   ├── create.rs        # Project creation from templates
│   ├── exec.rs          # Binary execution
│   ├── tree.rs          # Dependency tree display
│   ├── outdated.rs      # Outdated package checking
│   ├── test.rs          # Test runner
│   ├── start.rs         # Development server
│   └── build.rs         # Build system integration
├── cache.rs             # Package caching system
├── cli.rs               # CLI utilities
├── errors.rs            # Error types with thiserror
├── extractor.rs         # Package extraction (tgz)
├── installer.rs         # Package installation logic
├── resolver.rs          # Dependency resolution
├── types.rs             # Data structures and types
├── ui.rs                # Terminal UI with progress bars
└── utils.rs             # Utility functions
```

## Key Features Implemented
- ⚡ Ultra-fast parallel package installation
- 🎨 Beautiful terminal UI with progress bars and colors
- 💾 Intelligent package caching with integrity checking
- 🔒 Security auditing with vulnerability detection
- 📦 npm registry integration for search and metadata
- 🔄 Drop-in replacement for npm commands
- 🌍 Cross-platform support (Windows, macOS, Linux)
- 📋 Comprehensive error handling and logging
- 🚀 Async/await throughout for maximum performance

## Architecture
- **Modular design** with separate command handlers
- **Async runtime** using Tokio for concurrent operations
- **Type-safe** with comprehensive Rust type definitions
- **Error handling** with anyhow for rich error context
- **Configuration management** for user preferences
- **UI abstraction** for consistent user experience

## Dependencies
Key Rust crates:
- `clap` - CLI argument parsing
- `tokio` - Async runtime
- `reqwest` - HTTP client
- `serde` - JSON serialization
- `semver` - Version handling
- `indicatif` - Progress bars
- `colored` - Terminal colors
- `tar` + `flate2` - Archive extraction
- `blake3` - Cryptographic hashing
- `anyhow` + `thiserror` - Error handling

## Compilation Issues to Fix
The current build has ~150 compilation errors that need fixing:
1. Type mismatches in package.json field access
2. Missing method implementations in cache/resolver
3. Incorrect error constructor calls
4. Async/await pattern issues
5. Borrowing conflicts in tree display

## npm Publishing Structure
The build creates a npm package with:
- Cross-platform binaries in `binaries/` directory
- Platform detection and installation script
- Proper npm package.json with bin entries
- CLI wrapper that spawns the appropriate binary

## Usage Examples
```bash
# Install packages
nx install express
nx i lodash --save-dev

# Remove packages
nx remove express
nx rm lodash

# Update packages
nx update
nx outdated

# Run scripts
nx run start
nx run build

# Project management
nx init my-project --typescript
nx clean --all
nx audit --fix

# Registry operations
nx search react
nx show express
nx publish --dry-run
```

## Performance Goals
- 10x faster than npm for typical operations
- Parallel downloads with connection pooling
- Smart caching to avoid redundant downloads
- Efficient dependency resolution algorithm
- Minimal memory footprint despite being feature-rich
