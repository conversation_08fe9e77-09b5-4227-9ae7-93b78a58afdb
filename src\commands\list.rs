//! List command implementation for installed packages
//! 
//! This module handles listing installed packages with tree view support.

use crate::types::PackageJson;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use std::path::Path;
use std::time::Instant;
use tokio::fs;

pub async fn handle_list(cmd: crate::ListCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if cmd.global {
        list_global_packages(cmd.tree, verbose).await?;
    } else {
        list_local_packages(cmd.tree, cmd.outdated, verbose).await?;
    }
    
    let elapsed = start_time.elapsed();
    if verbose {
        println!();
        println!("{} Listed packages in {:.2}s", 
            "✨".bright_yellow(),
            elapsed.as_secs_f64()
        );
    }
    
    Ok(())
}

async fn list_local_packages(show_tree: bool, show_outdated: bool, verbose: bool) -> Result<()> {
    let package_json_path = Path::new("package.json");
    let node_modules_path = Path::new("node_modules");
    
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    if !node_modules_path.exists() {
        ui::show_info("No node_modules directory found. Run 'nx install' to install dependencies.");
        return Ok(());
    }
    
    // Show project info
    if let Some(ref name) = package_json.name {
        print!("{}", name.bright_green().bold());
        if let Some(ref version) = package_json.version {
            print!(" {}", format!("v{}", version).bright_blue());
        }
        println!();
    }
    
    if let Some(ref description) = package_json.description {
        println!("{}", description.white());
    }
    
    if show_tree {
        display_dependency_tree(&package_json, node_modules_path, verbose).await?;
    } else {
        display_flat_list(&package_json, node_modules_path, show_outdated, verbose).await?;
    }
    
    Ok(())
}

async fn list_global_packages(show_tree: bool, verbose: bool) -> Result<()> {
    let global_dir = utils::get_global_packages_dir()?;
    
    if !global_dir.exists() {
        ui::show_info("No global packages installed");
        return Ok(());
    }
    
    println!("{}", "Global packages:".bright_cyan().bold());
    println!();
    
    let mut packages = Vec::new();
    let mut entries = fs::read_dir(&global_dir).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let name = entry.file_name().to_string_lossy().to_string();
        
        if name.starts_with('.') || name == "bin" {
            continue;
        }
        
        let package_json_path = entry.path().join("package.json");
        if package_json_path.exists() {
            let content = fs::read_to_string(&package_json_path).await?;
            if let Ok(pkg_json) = serde_json::from_str::<PackageJson>(&content) {
                packages.push((name, pkg_json));
            }
        } else {
            // Package without package.json, just show the name
            packages.push((name, PackageJson {
                name: None,
                version: None,
                description: None,
                main: None,
                scripts: None,
                dependencies: None,
                dev_dependencies: None,
                peer_dependencies: None,
                optional_dependencies: None,
                keywords: None,
                author: None,
                license: None,
                homepage: None,
                repository: None,
                bugs: None,
                engines: None,
                bin: None,
            }));
        }
    }
    
    packages.sort_by(|a, b| a.0.cmp(&b.0));
    
    for (dir_name, pkg_json) in packages {
        let name = pkg_json.name.as_deref().unwrap_or(&dir_name);
        let version = pkg_json.version.as_deref().unwrap_or("unknown");
        
        println!("  {} {}",
            name.bright_green(),
            format!("v{}", version).bright_blue()
        );
        
        if verbose {
            if let Some(ref description) = pkg_json.description {
                println!("    {}", description.dimmed());
            }
        }
    }
    
    Ok(())
}

async fn display_flat_list(
    package_json: &PackageJson,
    node_modules_path: &Path,
    show_outdated: bool,
    verbose: bool,
) -> Result<()> {
    let mut all_deps = HashMap::new();
    let mut installed_packages = HashMap::new();
    
    // Collect declared dependencies
    if let Some(ref deps) = package_json.dependencies {
        for (name, version) in deps {
            all_deps.insert(name.clone(), (version.clone(), "dependency".to_string()));
        }
    }
    
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        for (name, version) in dev_deps {
            all_deps.insert(name.clone(), (version.clone(), "devDependency".to_string()));
        }
    }
    
    if let Some(ref optional_deps) = package_json.optional_dependencies {
        for (name, version) in optional_deps {
            all_deps.insert(name.clone(), (version.clone(), "optionalDependency".to_string()));
        }
    }
    
    // Scan installed packages
    let mut entries = fs::read_dir(node_modules_path).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let name = entry.file_name().to_string_lossy().to_string();
        
        if name.starts_with('.') {
            continue;
        }
        
        if name.starts_with('@') {
            // Scoped packages
            let mut scoped_entries = fs::read_dir(entry.path()).await?;
            while let Some(scoped_entry) = scoped_entries.next_entry().await? {
                let scoped_name = scoped_entry.file_name().to_string_lossy().to_string();
                let full_name = format!("{}/{}", name, scoped_name);
                
                if let Some(pkg_info) = read_package_info(&scoped_entry.path()).await? {
                    installed_packages.insert(full_name, pkg_info);
                }
            }
        } else {
            if let Some(pkg_info) = read_package_info(&entry.path()).await? {
                installed_packages.insert(name, pkg_info);
            }
        }
    }
    
    // Display dependencies
    if !all_deps.is_empty() {
        println!();
        println!("{}", "Dependencies:".bright_cyan().bold());
        
        for (name, (declared_version, dep_type)) in &all_deps {
            let type_color = match dep_type.as_str() {
                "devDependency" => "dev".bright_yellow(),
                "optionalDependency" => "optional".bright_magenta(),
                _ => "prod".bright_green(),
            };
            
            if let Some((installed_version, _)) = installed_packages.get(name) {
                println!("  {} {} {} {}",
                    name.bright_white(),
                    format!("v{}", installed_version).bright_blue(),
                    format!("({})", declared_version).dimmed(),
                    format!("[{}]", type_color)
                );
            } else {
                println!("  {} {} {} {}",
                    name.bright_red(),
                    "MISSING".bright_red().bold(),
                    format!("({})", declared_version).dimmed(),
                    format!("[{}]", type_color)
                );
            }
        }
    }
    
    // Show extraneous packages
    let declared_names: HashSet<_> = all_deps.keys().cloned().collect();
    let installed_names: HashSet<_> = installed_packages.keys().cloned().collect();
    let extraneous: Vec<_> = installed_names.difference(&declared_names).collect();
    
    if !extraneous.is_empty() {
        println!();
        println!("{}", "Extraneous packages:".bright_yellow().bold());
        
        for name in extraneous {
            if let Some((version, _)) = installed_packages.get(*name) {
                println!("  {} {}",
                    name.bright_yellow(),
                    format!("v{}", version).bright_blue()
                );
            }
        }
    }
    
    Ok(())
}

async fn display_dependency_tree(
    package_json: &PackageJson,
    node_modules_path: &Path,
    verbose: bool,
) -> Result<()> {
    println!();
    println!("{}", "Dependency tree:".bright_cyan().bold());
    
    let mut all_deps = HashMap::new();
    
    if let Some(ref deps) = package_json.dependencies {
        for (name, version) in deps {
            all_deps.insert(name.clone(), version.clone());
        }
    }
    
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        for (name, version) in dev_deps {
            all_deps.insert(name.clone(), version.clone());
        }
    }
    
    let mut visited = HashSet::new();
    
    for (name, _) in &all_deps {
        if !visited.contains(name) {
            display_package_tree(name, node_modules_path, 0, &mut visited, verbose).await?;
        }
    }
    
    Ok(())
}

async fn display_package_tree(
    package_name: &str,
    node_modules_base: &Path,
    depth: usize,
    visited: &mut HashSet<String>,
    verbose: bool,
) -> Result<()> {
    if visited.contains(package_name) {
        return Ok(());
    }
    
    visited.insert(package_name.to_string());
    
    let indent = "  ".repeat(depth);
    let package_path = node_modules_base.join(package_name);
    
    if !package_path.exists() {
        println!("{}├─ {} {}", 
            indent, 
            package_name.bright_red(), 
            "MISSING".bright_red().bold()
        );
        return Ok(());
    }
    
    if let Some((version, description)) = read_package_info(&package_path).await? {
        println!("{}├─ {} {}",
            indent,
            package_name.bright_green(),
            format!("v{}", version).bright_blue()
        );
        
        if verbose && !description.is_empty() {
            println!("{}│  {}", indent, description.dimmed());
        }
        
        // Show dependencies of this package
        let pkg_json_path = package_path.join("package.json");
        if pkg_json_path.exists() {
            let content = fs::read_to_string(&pkg_json_path).await?;
            if let Ok(pkg_json) = serde_json::from_str::<PackageJson>(&content) {
                if let Some(ref deps) = pkg_json.dependencies {
                    for dep_name in deps.keys() {
                        display_package_tree(dep_name, node_modules_base, depth + 1, visited, verbose).await?;
                    }
                }
            }
        }
    } else {
        println!("{}├─ {} {}", 
            indent, 
            package_name.bright_yellow(), 
            "INVALID".bright_yellow().bold()
        );
    }
    
    Ok(())
}

async fn read_package_info(package_path: &Path) -> Result<Option<(String, String)>> {
    let pkg_json_path = package_path.join("package.json");
    
    if !pkg_json_path.exists() {
        return Ok(None);
    }
    
    let content = fs::read_to_string(&pkg_json_path).await?;
    let pkg_json: Result<PackageJson, _> = serde_json::from_str(&content);
    
    match pkg_json {
        Ok(json) => {
            let version = json.version.unwrap_or_else(|| "unknown".to_string());
            let description = json.description.unwrap_or_default();
            Ok(Some((version, description)))
        },
        Err(_) => Ok(None),
    }
}
