//! Command Line Interface definitions
//! 
//! This module defines all CLI commands, arguments, and options for the nx package manager.

use clap::{Parser, Subcommand};

#[derive(Parser)]
#[command(
    name = "nx",
    version = "0.2.0",
    about = "Ultra-fast package manager for Node.js ecosystems - Built in Rust",
    long_about = "NX is a blazing-fast package manager designed to outperform npm, yarn, pnpm, and bun.\nBuilt in Rust for maximum performance with beautiful UI and comprehensive features."
)]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,
    
    /// Enable verbose output
    #[arg(short, long, global = true)]
    pub verbose: bool,
    
    /// Disable colored output
    #[arg(long, global = true)]
    pub no_color: bool,
    
    /// Set registry URL
    #[arg(long, global = true)]
    pub registry: Option<String>,
}

#[derive(Subcommand)]
pub enum Commands {
    /// Install packages with ultra-fast parallel downloads
    #[command(alias = "i")]
    Install {
        /// Package names to install (e.g., express@4.18.2)
        packages: Vec<String>,
        
        /// Install globally
        #[arg(short, long)]
        global: bool,
        
        /// Save as dev dependency
        #[arg(short = 'D', long)]
        save_dev: bool,
        
        /// Dry run - don't actually install
        #[arg(long)]
        dry_run: bool,
        
        /// Only install production dependencies
        #[arg(long)]
        production: bool,
    },
    
    /// Uninstall packages with dependency cleanup
    #[command(alias = "remove", alias = "rm")]
    Uninstall {
        /// Package names to uninstall
        packages: Vec<String>,
        
        /// Uninstall globally
        #[arg(short, long)]
        global: bool,
    },
    
    /// Update packages to latest versions
    #[command(alias = "upgrade")]
    Update {
        /// Package names to update (empty = update all)
        packages: Vec<String>,
        
        /// Update globally installed packages
        #[arg(short, long)]
        global: bool,
        
        /// Update to latest tag (ignoring semver)
        #[arg(long)]
        latest: bool,
    },
    
    /// Clean install from lockfile (CI environments)
    Ci {
        /// Clean node_modules before install
        #[arg(long)]
        clean: bool,
        
        /// Fail if lockfile is out of date
        #[arg(long)]
        frozen_lockfile: bool,
    },
    
    /// List installed packages with beautiful formatting
    #[command(alias = "ls")]
    List {
        /// List globally installed packages
        #[arg(short, long)]
        global: bool,
        
        /// Show as dependency tree
        #[arg(long)]
        tree: bool,
        
        /// Tree depth limit
        #[arg(long, default_value = "3")]
        depth: usize,
    },
    
    /// Show detailed package information
    Info {
        /// Package name to get info for
        package: String,
        
        /// Show all available versions
        #[arg(long)]
        versions: bool,
    },
    
    /// Show outdated packages with update recommendations
    Outdated {
        /// Check globally installed packages
        #[arg(short, long)]
        global: bool,
    },
    
    /// Security audit with vulnerability detection
    Audit {
        /// Auto-fix vulnerabilities where possible
        #[arg(long)]
        fix: bool,
        
        /// Output results in JSON format
        #[arg(long)]
        json: bool,
    },
    
    /// Run package scripts with enhanced output
    Run {
        /// Script name to run
        script: String,
        
        /// Arguments to pass to script
        args: Vec<String>,
    },
    
    /// Execute package binaries
    Exec {
        /// Binary name to execute
        binary: String,
        
        /// Arguments to pass to binary
        args: Vec<String>,
    },
    
    /// Rebuild native modules and binaries
    Rebuild,
    
    /// Remove duplicate dependencies (deduplication)
    Dedupe,
    
    /// Advanced cache management
    Cache {
        #[command(subcommand)]
        action: CacheAction,
    },
    
    /// Initialize a new package.json
    Init {
        /// Package name
        #[arg(short, long)]
        name: Option<String>,
        
        /// Package description
        #[arg(short, long)]
        description: Option<String>,
    },
    
    /// Publish package to registry
    Publish {
        /// Distribution tag
        #[arg(long, default_value = "latest")]
        tag: String,
        
        /// Package access level
        #[arg(long)]
        access: Option<String>,
    },
    
    /// Configuration management
    Config {
        /// Configuration key
        key: Option<String>,
        
        /// Configuration value
        value: Option<String>,
        
        /// List all configuration
        #[arg(short, long)]
        list: bool,
    },
    
    /// Health check and diagnostics
    Doctor,
}

#[derive(Subcommand)]
pub enum CacheAction {
    /// Clear all cached data
    Clear,
    
    /// Show cache statistics and usage
    Stats,
    
    /// Verify cache integrity
    Verify,
}
