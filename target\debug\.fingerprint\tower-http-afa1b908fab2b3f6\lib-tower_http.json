{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 10809724437792986082, "path": 16215392639322834235, "deps": [[784494742817713399, "tower_service", false, 14798241922623160777], [1906322745568073236, "pin_project_lite", false, 17716912910420925338], [4121350475192885151, "iri_string", false, 5582253582127097592], [5695049318159433696, "tower", false, 7634859041208279789], [7712452662827335977, "tower_layer", false, 5933096179673301454], [7896293946984509699, "bitflags", false, 6562595423089658452], [9010263965687315507, "http", false, 16923173100736676894], [10629569228670356391, "futures_util", false, 14200520146607673690], [14084095096285906100, "http_body", false, 9440646934113007432], [16066129441945555748, "bytes", false, 15233928210577460823]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-afa1b908fab2b3f6\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}