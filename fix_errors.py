#!/usr/bin/env python3
"""
Quick fix script to resolve common Rust compilation errors
"""

import os
import re

def fix_dim_methods(file_path):
    """Fix .dim() calls to .dimmed()"""
    if not os.path.exists(file_path):
        return
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace .dim() with .dimmed()
    content = re.sub(r'\.dim\(\)', r'.dimmed()', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed dim() methods in {file_path}")

def fix_show_version(file_path):
    """Fix the show.rs version display issue"""
    if not os.path.exists(file_path):
        return
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix the version display issue
    old_pattern = r'''if version\.version != "latest" \{
            format!\("@\{\}", version\.version\)\.bright_blue\(\)
        \} else \{
            String::new\(\)
        \}'''
    
    new_pattern = '''if version.version != "latest" {
            format!("@{}", version.version).bright_blue().to_string()
        } else {
            String::new()
        }'''
    
    content = re.sub(old_pattern, new_pattern, content, flags=re.MULTILINE)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed version display in {file_path}")

def main():
    # List of files to fix dim() methods
    files_to_fix = [
        "src/commands/show.rs",
        "src/commands/search.rs", 
        "src/commands/run.rs",
        "src/commands/init.rs",
        "src/commands/logout.rs",
        "src/commands/audit.rs",
        "src/commands/create.rs",
        "src/commands/tree.rs",
        "src/commands/outdated.rs",
        "src/commands/test.rs",
        "src/commands/start.rs",
        "src/commands/build.rs"
    ]
    
    for file_path in files_to_fix:
        fix_dim_methods(file_path)
    
    # Fix specific show.rs issue
    fix_show_version("src/commands/show.rs")
    
    print("All fixes applied!")

if __name__ == "__main__":
    main()
