use thiserror::Error;

#[derive(Error, Debug)]
pub enum NxError {
    #[error("Network error: {message}")]
    Network { message: String },

    #[error("HTTP error: {status} - {message}")]
    Http { status: u16, message: String },

    #[error("Filesystem error: {message}")]
    Filesystem { message: String },

    #[error("Package not found: {package}")]
    PackageNotFound { package: String },

    #[error("Version not found: {package}@{version}")]
    VersionNotFound { package: String, version: String },

    #[error("Invalid package specification: {spec}")]
    InvalidPackageSpec { spec: String },

    #[error("Invalid version: {version}")]
    InvalidVersion { version: String },

    #[error("Dependency resolution failed: {message}")]
    ResolutionFailed { message: String },

    #[error("Circular dependency detected: {path}")]
    CircularDependency { path: String },

    #[error("Extraction failed: {package} - {reason}")]
    ExtractionFailed { package: String, reason: String },

    #[error("Validation error: {message}")]
    ValidationError { message: String },

    #[error("Authentication error: {message}")]
    AuthError { message: String },

    #[error("Registry error: {registry} - {message}")]
    RegistryError { registry: String, message: String },

    #[error("Configuration error: {message}")]
    ConfigError { message: String },

    #[error("Permission denied: {path}")]
    PermissionDenied { path: String },

    #[error("Cache error: {message}")]
    CacheError { message: String },

    #[error("Integrity check failed: {package} - expected {expected}, got {actual}")]
    IntegrityMismatch {
        package: String,
        expected: String,
        actual: String,
    },

    #[error("Script execution failed: {script} - {message}")]
    ScriptFailed { script: String, message: String },

    #[error("Timeout occurred: {operation}")]
    Timeout { operation: String },

    #[error("JSON parsing error: {message}")]
    JsonError { message: String },

    #[error("IO error: {message}")]
    IoError { message: String },

    #[error("Semver error: {message}")]
    SemverError { message: String },

    #[error("Template error: {template} - {message}")]
    TemplateError { template: String, message: String },

    #[error("Project already exists: {path}")]
    ProjectExists { path: String },

    #[error("Not a project directory: {path}")]
    NotProjectDirectory { path: String },

    #[error("Command not found: {command}")]
    CommandNotFound { command: String },

    #[error("Audit failed: {message}")]
    AuditFailed { message: String },

    #[error("Publish failed: {message}")]
    PublishFailed { message: String },

    #[error("Link error: {message}")]
    LinkError { message: String },

    #[error("Internal error: {message}")]
    Internal { message: String },
}

impl NxError {
    pub fn network(message: impl Into<String>) -> Self {
        Self::Network {
            message: message.into(),
        }
    }

    pub fn http(status: u16, message: impl Into<String>) -> Self {
        Self::Http {
            status,
            message: message.into(),
        }
    }

    pub fn filesystem(message: impl Into<String>) -> Self {
        Self::Filesystem {
            message: message.into(),
        }
    }

    pub fn package_not_found(package: impl Into<String>) -> Self {
        Self::PackageNotFound {
            package: package.into(),
        }
    }

    pub fn version_not_found(package: impl Into<String>, version: impl Into<String>) -> Self {
        Self::VersionNotFound {
            package: package.into(),
            version: version.into(),
        }
    }

    pub fn invalid_package_spec(spec: impl Into<String>) -> Self {
        Self::InvalidPackageSpec {
            spec: spec.into(),
        }
    }

    pub fn invalid_version(version: impl Into<String>) -> Self {
        Self::InvalidVersion {
            version: version.into(),
        }
    }

    pub fn resolution_failed(message: impl Into<String>) -> Self {
        Self::ResolutionFailed {
            message: message.into(),
        }
    }

    pub fn circular_dependency(path: impl Into<String>) -> Self {
        Self::CircularDependency {
            path: path.into(),
        }
    }

    pub fn extraction_failed(package: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::ExtractionFailed {
            package: package.into(),
            reason: reason.into(),
        }
    }

    pub fn validation(message: impl Into<String>) -> Self {
        Self::ValidationError {
            message: message.into(),
        }
    }

    pub fn auth_error(message: impl Into<String>) -> Self {
        Self::AuthError {
            message: message.into(),
        }
    }

    pub fn registry_error(registry: impl Into<String>, message: impl Into<String>) -> Self {
        Self::RegistryError {
            registry: registry.into(),
            message: message.into(),
        }
    }

    pub fn config_error(message: impl Into<String>) -> Self {
        Self::ConfigError {
            message: message.into(),
        }
    }

    pub fn permission_denied(path: impl Into<String>) -> Self {
        Self::PermissionDenied {
            path: path.into(),
        }
    }

    pub fn cache_error(message: impl Into<String>) -> Self {
        Self::CacheError {
            message: message.into(),
        }
    }

    pub fn integrity_mismatch(
        package: impl Into<String>,
        expected: impl Into<String>,
        actual: impl Into<String>,
    ) -> Self {
        Self::IntegrityMismatch {
            package: package.into(),
            expected: expected.into(),
            actual: actual.into(),
        }
    }

    pub fn script_failed(script: impl Into<String>, message: impl Into<String>) -> Self {
        Self::ScriptFailed {
            script: script.into(),
            message: message.into(),
        }
    }

    pub fn timeout(operation: impl Into<String>) -> Self {
        Self::Timeout {
            operation: operation.into(),
        }
    }

    pub fn json_error(message: impl Into<String>) -> Self {
        Self::JsonError {
            message: message.into(),
        }
    }

    pub fn io_error(message: impl Into<String>) -> Self {
        Self::IoError {
            message: message.into(),
        }
    }

    pub fn semver_error(message: impl Into<String>) -> Self {
        Self::SemverError {
            message: message.into(),
        }
    }

    pub fn template_error(template: impl Into<String>, message: impl Into<String>) -> Self {
        Self::TemplateError {
            template: template.into(),
            message: message.into(),
        }
    }

    pub fn project_exists(path: impl Into<String>) -> Self {
        Self::ProjectExists {
            path: path.into(),
        }
    }

    pub fn not_project_directory(path: impl Into<String>) -> Self {
        Self::NotProjectDirectory {
            path: path.into(),
        }
    }

    pub fn command_not_found(command: impl Into<String>) -> Self {
        Self::CommandNotFound {
            command: command.into(),
        }
    }

    pub fn audit_failed(message: impl Into<String>) -> Self {
        Self::AuditFailed {
            message: message.into(),
        }
    }

    pub fn publish_failed(message: impl Into<String>) -> Self {
        Self::PublishFailed {
            message: message.into(),
        }
    }

    pub fn link_error(message: impl Into<String>) -> Self {
        Self::LinkError {
            message: message.into(),
        }
    }

    pub fn internal(message: impl Into<String>) -> Self {
        Self::Internal {
            message: message.into(),
        }
    }
}

// Conversion from std errors
impl From<std::io::Error> for NxError {
    fn from(err: std::io::Error) -> Self {
        Self::IoError {
            message: err.to_string(),
        }
    }
}

impl From<serde_json::Error> for NxError {
    fn from(err: serde_json::Error) -> Self {
        Self::JsonError {
            message: err.to_string(),
        }
    }
}

impl From<reqwest::Error> for NxError {
    fn from(err: reqwest::Error) -> Self {
        if err.is_timeout() {
            Self::Timeout {
                operation: "HTTP request".to_string(),
            }
        } else if err.is_connect() {
            Self::Network {
                message: format!("Connection failed: {}", err),
            }
        } else if let Some(status) = err.status() {
            Self::Http {
                status: status.as_u16(),
                message: err.to_string(),
            }
        } else {
            Self::Network {
                message: err.to_string(),
            }
        }
    }
}

impl From<semver::Error> for NxError {
    fn from(err: semver::Error) -> Self {
        Self::SemverError {
            message: err.to_string(),
        }
    }
}

impl From<walkdir::Error> for NxError {
    fn from(err: walkdir::Error) -> Self {
        Self::Filesystem {
            message: err.to_string(),
        }
    }
}

impl From<tar::Error> for NxError {
    fn from(err: tar::Error) -> Self {
        Self::ExtractionFailed {
            package: "unknown".to_string(),
            reason: err.to_string(),
        }
    }
}

// Result type alias for convenience
pub type NxResult<T> = Result<T, NxError>;
