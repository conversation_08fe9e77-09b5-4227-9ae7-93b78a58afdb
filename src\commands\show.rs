//! Show command implementation for package information
//! 
//! This module handles displaying detailed information about packages.

use crate::ui;
use anyhow::{Result, Context};
use colored::*;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use std::time::Instant;

#[derive(Debug, Deserialize)]
struct PackageManifest {
    name: String,
    description: Option<String>,
    #[serde(rename = "dist-tags")]
    dist_tags: Option<HashMap<String, String>>,
    versions: HashMap<String, PackageVersion>,
    time: Option<HashMap<String, String>>,
    author: Option<Value>,
    maintainers: Option<Vec<Maintainer>>,
    license: Option<Value>,
    homepage: Option<String>,
    repository: Option<Value>,
    bugs: Option<Value>,
    keywords: Option<Vec<String>>,
    readme: Option<String>,
}

#[derive(Debug, Deserialize)]
struct PackageVersion {
    name: String,
    version: String,
    description: Option<String>,
    main: Option<String>,
    scripts: Option<HashMap<String, String>>,
    dependencies: Option<HashMap<String, String>>,
    #[serde(rename = "devDependencies")]
    dev_dependencies: Option<HashMap<String, String>>,
    #[serde(rename = "peerDependencies")]
    peer_dependencies: Option<HashMap<String, String>>,
    #[serde(rename = "optionalDependencies")]
    optional_dependencies: Option<HashMap<String, String>>,
    engines: Option<HashMap<String, String>>,
    license: Option<Value>,
    author: Option<Value>,
    dist: Option<DistInfo>,
    #[serde(rename = "_id")]
    id: Option<String>,
    #[serde(rename = "_nodeVersion")]
    node_version: Option<String>,
    #[serde(rename = "_npmVersion")]
    npm_version: Option<String>,
}

#[derive(Debug, Deserialize)]
struct DistInfo {
    tarball: Option<String>,
    shasum: Option<String>,
    integrity: Option<String>,
    #[serde(rename = "unpackedSize")]
    unpacked_size: Option<u64>,
    #[serde(rename = "fileCount")]
    file_count: Option<u32>,
}

#[derive(Debug, Deserialize)]
struct Maintainer {
    name: String,
    email: Option<String>,
}

pub async fn handle_show(cmd: crate::ShowCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if verbose {
        println!("{} Fetching information for '{}'...", 
            "info".cyan().bold(),
            cmd.package.bright_white()
        );
    }
    
    let client = Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .context("Failed to create HTTP client")?;
    
    let package_url = format!(
        "https://registry.npmjs.org/{}",
        urlencoding::encode(&cmd.package)
    );
    
    let spinner = ui::create_resolution_spinner();
    spinner.set_message(format!("Fetching package info for '{}'", cmd.package));
    
    let response = client
        .get(&package_url)
        .header("User-Agent", "nx/0.2.0")
        .send()
        .await
        .context("Failed to fetch package information")?;
    
    spinner.finish_and_clear();
    
    if response.status() == 404 {
        ui::show_error(&format!("Package '{}' not found", cmd.package));
        return Ok(());
    }
    
    if !response.status().is_success() {
        ui::show_error(&format!("Failed to fetch package info: HTTP {}", response.status()));
        return Ok(());
    }
    
    let manifest: PackageManifest = response
        .json()
        .await
        .context("Failed to parse package manifest")?;
    
    let version_to_show = cmd.version
        .as_deref()
        .or_else(|| manifest.dist_tags.as_ref().and_then(|tags| tags.get("latest")))
        .unwrap_or("latest");
    
    let version_info = if let Some(version_data) = manifest.versions.get(version_to_show) {
        version_data
    } else {
        // Try to find the closest version
        let available_versions: Vec<_> = manifest.versions.keys().collect();
        ui::show_error(&format!(
            "Version '{}' not found. Available versions: {}",
            version_to_show,
            available_versions.join(", ")
        ));
        return Ok(());
    };
    
    let elapsed = start_time.elapsed();
    
    // Display package information
    display_package_info(&manifest, version_info, verbose, elapsed);
    
    Ok(())
}

fn display_package_info(
    manifest: &PackageManifest, 
    version: &PackageVersion, 
    verbose: bool,
    elapsed: std::time::Duration
) {
    println!();
    println!("{} {}", 
        manifest.name.bright_green().bold(),
        format!("v{}", version.version).bright_blue()
    );
    
    if let Some(ref description) = version.description.as_ref().or(manifest.description.as_ref()) {
        println!("{}", description.white());
    }
    
    println!();
    println!("{}", "─".repeat(80).dimmed());
    
    // Basic information
    println!();
    println!("{}", "📦 Package Information".bright_cyan().bold());
    
    if let Some(ref license) = version.license.as_ref().or(manifest.license.as_ref()) {
        let license_str = match license {
            Value::String(s) => s.clone(),
            Value::Object(obj) if obj.contains_key("type") => {
                obj["type"].as_str().unwrap_or("Unknown").to_string()
            },
            _ => "Unknown".to_string(),
        };
        println!("  {} {}", "License:".dimmed(), license_str.bright_white());
    }
    
    if let Some(ref homepage) = manifest.homepage {
        println!("  {} {}", "Homepage:".dimmed(), homepage.bright_blue().underline());
    }
    
    if let Some(ref repository) = manifest.repository {
        let repo_url = match repository {
            Value::String(url) => url.clone(),
            Value::Object(obj) if obj.contains_key("url") => {
                obj["url"].as_str().unwrap_or("").to_string()
            },
            _ => String::new(),
        };
        if !repo_url.is_empty() {
            println!("  {} {}", "Repository:".dimmed(), repo_url.bright_blue().underline());
        }
    }
    
    // Author information
    if let Some(ref author) = version.author.as_ref().or(manifest.author.as_ref()) {
        let author_str = match author {
            Value::String(s) => s.clone(),
            Value::Object(obj) => {
                let name = obj.get("name").and_then(|v| v.as_str()).unwrap_or("");
                let email = obj.get("email").and_then(|v| v.as_str()).unwrap_or("");
                if !email.is_empty() {
                    format!("{} <{}>", name, email)
                } else {
                    name.to_string()
                }
            },
            _ => "Unknown".to_string(),
        };
        println!("  {} {}", "Author:".dimmed(), author_str.bright_white());
    }
    
    // Maintainers (only in verbose mode)
    if verbose {
        if let Some(ref maintainers) = manifest.maintainers {
            if !maintainers.is_empty() {
                println!("  {} {}", "Maintainers:".dimmed(), 
                    maintainers.iter()
                        .map(|m| if let Some(ref email) = m.email {
                            format!("{} <{}>", m.name, email)
                        } else {
                            m.name.clone()
                        })
                        .collect::<Vec<_>>()
                        .join(", ")
                        .bright_white()
                );
            }
        }
    }
    
    // Keywords
    if let Some(ref keywords) = manifest.keywords {
        if !keywords.is_empty() {
            println!("  {} {}", "Keywords:".dimmed(), 
                keywords.iter()
                    .map(|k| k.bright_yellow().to_string())
                    .collect::<Vec<_>>()
                    .join(", ")
            );
        }
    }
    
    // Distribution information
    if let Some(ref dist) = version.dist {
        println!();
        println!("{}", "📊 Distribution".bright_cyan().bold());
        
        if let Some(unpacked_size) = dist.unpacked_size {
            println!("  {} {}", "Unpacked Size:".dimmed(), 
                ui::format_bytes(unpacked_size).bright_white());
        }
        
        if let Some(file_count) = dist.file_count {
            println!("  {} {}", "File Count:".dimmed(), 
                file_count.to_string().bright_white());
        }
        
        if let Some(ref tarball) = dist.tarball {
            println!("  {} {}", "Tarball:".dimmed(), tarball.bright_blue().underline());
        }
        
        if verbose {
            if let Some(ref shasum) = dist.shasum {
                println!("  {} {}", "SHA1:".dimmed(), shasum.bright_white());
            }
            
            if let Some(ref integrity) = dist.integrity {
                println!("  {} {}", "Integrity:".dimmed(), integrity.bright_white());
            }
        }
    }
    
    // Dependencies
    if let Some(ref deps) = version.dependencies {
        if !deps.is_empty() {
            println!();
            println!("{} ({})", "🔗 Dependencies".bright_cyan().bold(), deps.len());
            
            for (name, version_spec) in deps {
                println!("  {} {}", name.bright_white(), version_spec.dimmed());
            }
        }
    }
    
    // Dev dependencies (only in verbose mode)
    if verbose {
        if let Some(ref dev_deps) = version.dev_dependencies {
            if !dev_deps.is_empty() {
                println!();
                println!("{} ({})", "🛠️ Dev Dependencies".bright_cyan().bold(), dev_deps.len());
                
                for (name, version_spec) in dev_deps {
                    println!("  {} {}", name.bright_white(), version_spec.dimmed());
                }
            }
        }
    }
    
    // Peer dependencies
    if let Some(ref peer_deps) = version.peer_dependencies {
        if !peer_deps.is_empty() {
            println!();
            println!("{} ({})", "👥 Peer Dependencies".bright_cyan().bold(), peer_deps.len());
            
            for (name, version_spec) in peer_deps {
                println!("  {} {}", name.bright_white(), version_spec.dimmed());
            }
        }
    }
    
    // Scripts (only in verbose mode)
    if verbose {
        if let Some(ref scripts) = version.scripts {
            if !scripts.is_empty() {
                println!();
                println!("{} ({})", "📜 Scripts".bright_cyan().bold(), scripts.len());
                
                for (name, command) in scripts {
                    println!("  {} {}", name.bright_green(), command.dimmed());
                }
            }
        }
    }
    
    // Engines
    if let Some(ref engines) = version.engines {
        if !engines.is_empty() {
            println!();
            println!("{}", "⚙️ Engines".bright_cyan().bold());
            
            for (engine, version_spec) in engines {
                println!("  {} {}", engine.bright_white(), version_spec.dimmed());
            }
        }
    }
    
    // Installation command
    println!();
    println!("{}", "📥 Installation".bright_cyan().bold());
    println!("  {} {} {}", 
        "nx install".bright_cyan(),
        manifest.name.bright_white(),
        if version.version != "latest" {
            format!("@{}", version.version).bright_blue().to_string()
        } else {
            String::new()
        }
    );
    
    println!();
    println!("{} Fetched in {:.2}s", 
        "✨".bright_yellow(),
        elapsed.as_secs_f64()
    );
}
