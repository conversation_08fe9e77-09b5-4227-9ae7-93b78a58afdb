#!/bin/bash

# Ultra-fast NX Package Manager Publishing Script
# This script publishes the built NX package manager to npm

set -e

echo "🚀 Publishing NX Package Manager to npm..."

# Check if we're in the right directory
if [ ! -f "Cargo.toml" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "❌ Error: dist directory not found. Run build script first."
    exit 1
fi

# Build for all platforms
echo "🔨 Building for all platforms..."
./scripts/build.ps1 -Release -Target all

# Check npm authentication
echo "🔐 Checking npm authentication..."
if ! npm whoami > /dev/null 2>&1; then
    echo "❌ Error: Not logged into npm. Please run 'npm login' first."
    exit 1
fi

NPM_USER=$(npm whoami)
echo "✅ Logged in as: $NPM_USER"

# Navigate to dist directory
cd dist

# Verify package structure
echo "📋 Verifying package structure..."
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found in dist directory"
    exit 1
fi

if [ ! -d "binaries" ]; then
    echo "❌ Error: binaries directory not found"
    exit 1
fi

echo "📦 Package contents:"
ls -la

echo "🔍 Binaries:"
ls -la binaries/

# Run npm pack to test package
echo "📦 Testing package creation..."
npm pack --dry-run

# Ask for confirmation
echo ""
echo "🤔 Ready to publish NX Package Manager?"
echo "   Package: $(cat package.json | grep '"name"' | cut -d'"' -f4)"
echo "   Version: $(cat package.json | grep '"version"' | cut -d'"' -f4)"
echo "   User: $NPM_USER"
echo ""
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Publishing cancelled"
    exit 1
fi

# Publish to npm
echo "🚀 Publishing to npm..."
npm publish --access public

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Successfully published NX Package Manager!"
    echo ""
    echo "📖 Installation instructions:"
    echo "   npm install -g nx-pm"
    echo ""
    echo "🔗 Package URL:"
    echo "   https://www.npmjs.com/package/nx-pm"
    echo ""
else
    echo "❌ Failed to publish package"
    exit 1
fi
