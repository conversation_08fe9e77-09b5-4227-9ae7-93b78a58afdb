//! Search command implementation with npm registry integration
//! 
//! This module handles searching for packages in the npm registry.

use crate::ui;
use anyhow::{Result, Context};
use colored::*;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::time::Instant;

#[derive(Debug, Deserialize)]
struct SearchResult {
    objects: Vec<SearchObject>,
    total: u32,
    time: String,
}

#[derive(Debug, Deserialize)]
struct SearchObject {
    package: SearchPackage,
    score: SearchScore,
    searchScore: f64,
}

#[derive(Debug, Deserialize)]
struct SearchPackage {
    name: String,
    scope: Option<String>,
    version: String,
    description: Option<String>,
    keywords: Option<Vec<String>>,
    date: String,
    author: Option<SearchAuthor>,
    publisher: Option<SearchPublisher>,
    maintainers: Option<Vec<SearchMaintainer>>,
    links: Option<SearchLinks>,
}

#[derive(Debug, Deserialize)]
struct SearchAuthor {
    name: Option<String>,
    email: Option<String>,
    username: Option<String>,
}

#[derive(Debug, Deserialize)]
struct SearchPublisher {
    username: String,
    email: Option<String>,
}

#[derive(Debug, Deserialize)]
struct SearchMaintainer {
    username: String,
    email: Option<String>,
}

#[derive(Debug, Deserialize)]
struct SearchLinks {
    npm: Option<String>,
    homepage: Option<String>,
    repository: Option<String>,
    bugs: Option<String>,
}

#[derive(Debug, Deserialize)]
struct SearchScore {
    #[serde(rename = "final")]
    final_score: f64,
    detail: SearchScoreDetail,
}

#[derive(Debug, Deserialize)]
struct SearchScoreDetail {
    quality: f64,
    popularity: f64,
    maintenance: f64,
}

pub async fn handle_search(cmd: crate::SearchCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if verbose {
        println!("{} Searching for '{}'...", 
            "info".cyan().bold(),
            cmd.query.bright_white()
        );
    }
    
    let client = Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .context("Failed to create HTTP client")?;
    
    let search_url = format!(
        "https://registry.npmjs.org/-/v1/search?text={}&size={}&from=0",
        urlencoding::encode(&cmd.query),
        cmd.limit
    );
    
    let spinner = ui::create_resolution_spinner();
    spinner.set_message(format!("Searching npm registry for '{}'", cmd.query));
    
    let response = client
        .get(&search_url)
        .header("User-Agent", "nx/0.2.0")
        .send()
        .await
        .context("Failed to search npm registry")?;
    
    spinner.finish_and_clear();
    
    if !response.status().is_success() {
        ui::show_error(&format!("Search failed: HTTP {}", response.status()));
        return Ok(());
    }
    
    let search_result: SearchResult = response
        .json()
        .await
        .context("Failed to parse search results")?;
    
    let elapsed = start_time.elapsed();
    
    if search_result.objects.is_empty() {
        ui::show_info(&format!("No packages found for '{}'", cmd.query));
        return Ok(());
    }
    
    // Display results header
    println!();
    println!("{} Found {} packages for '{}' in {:.2}s", 
        "✨".bright_yellow(),
        search_result.total.to_string().bright_white().bold(),
        cmd.query.bright_cyan(),
        elapsed.as_secs_f64()
    );
    println!("{}", "─".repeat(80).dimmed());
    println!();
    
    // Display search results
    for (index, obj) in search_result.objects.iter().enumerate().take(cmd.limit) {
        display_package_result(&obj, index + 1, verbose);
        
        if index < search_result.objects.len() - 1 && index < cmd.limit - 1 {
            println!("{}", "─".repeat(80).dimmed());
        }
    }
    
    println!();
    if search_result.total > cmd.limit as u32 {
        println!("{} Showing {} of {} results. Use --limit to see more.", 
            "info".cyan(),
            cmd.limit.to_string().bright_white(),
            search_result.total.to_string().bright_white()
        );
    }
    
    Ok(())
}

fn display_package_result(obj: &SearchObject, index: usize, verbose: bool) {
    let package = &obj.package;
    
    // Package name and version
    println!("{:2}. {} {}",
        index.to_string().dimmed(),
        package.name.bright_green().bold(),
        format!("v{}", package.version).bright_blue()
    );
    
    // Description
    if let Some(ref description) = package.description {
        println!("    {}", description.white());
    }
    
    // Keywords
    if let Some(ref keywords) = package.keywords {
        if !keywords.is_empty() {
            let keyword_str = keywords
                .iter()
                .take(5)
                .map(|k| k.bright_yellow().to_string())
                .collect::<Vec<_>>()
                .join(", ");
            println!("    {} {}", "Keywords:".dimmed(), keyword_str);
        }
    }
    
    // Author/Publisher
    if let Some(ref author) = package.author {
        if let Some(ref name) = author.name {
            println!("    {} {}", "Author:".dimmed(), name.bright_white());
        }
    } else if let Some(ref publisher) = package.publisher {
        println!("    {} {}", "Publisher:".dimmed(), publisher.username.bright_white());
    }
    
    // Score information (only in verbose mode)
    if verbose {
        let score = &obj.score;
        println!("    {} {:.1}% (Q: {:.1}%, P: {:.1}%, M: {:.1}%)",
            "Score:".dimmed(),
            (score.final_score * 100.0).round(),
            (score.detail.quality * 100.0).round(),
            (score.detail.popularity * 100.0).round(),
            (score.detail.maintenance * 100.0).round()
        );
    }
    
    // Links
    if let Some(ref links) = package.links {
        let mut link_parts = Vec::new();
        
        if let Some(ref homepage) = links.homepage {
            link_parts.push(format!("{}: {}", "Homepage".dimmed(), homepage.bright_blue().underline()));
        }
        
        if let Some(ref repository) = links.repository {
            link_parts.push(format!("{}: {}", "Repository".dimmed(), repository.bright_blue().underline()));
        }
        
        if !link_parts.is_empty() {
            println!("    {}", link_parts.join(" • "));
        }
    }
    
    // Installation hint
    println!("    {} {} {}",
        "Install:".dimmed(),
        "nx install".bright_cyan(),
        package.name.bright_white()
    );
    
    println!();
}
