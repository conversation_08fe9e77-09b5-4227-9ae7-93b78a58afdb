//! Ultra-fast package installer with parallel downloads and installations
//! 
//! This module handles package installations with maximum parallelism,
//! intelligent caching, and robust error recovery.

use crate::cache::PackageCache;
use crate::errors::NxError;
use crate::extractor::TarballExtractor;
use crate::types::{InstallOptions, InstalledPackage, ResolvedPackage};
use crate::ui::{self, PackageStatus};
use crate::utils;
use anyhow::{Result, Context};
use rayon::prelude::*;
use std::collections::HashSet;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};
use std::time::Instant;
use tokio::sync::Semaphore;

pub struct PackageInstaller {
    cache: Arc<PackageCache>,
    extractor: TarballExtractor,
    semaphore: Arc<Semaphore>,
    installed_packages: Arc<Mutex<HashSet<String>>>,
}

impl PackageInstaller {
    pub fn new() -> Result<Self> {
        Ok(Self {
            cache: Arc::new(PackageCache::new()?),
            extractor: TarballExtractor::new(),
            semaphore: Arc::new(Semaphore::new(50)), // Allow 50 concurrent installations
            installed_packages: Arc::new(Mutex::new(HashSet::new())),
        })
    }

    /// Install packages with ultra-fast parallel processing
    pub async fn install_packages(
        &self,
        packages: &[ResolvedPackage],
        options: &InstallOptions,
    ) -> Result<()> {
        if options.dry_run {
            self.show_dry_run_summary(packages);
            return Ok(());
        }

        let start_time = Instant::now();
        let node_modules_dir = self.get_node_modules_dir(options.global)?;
        
        // Create node_modules directory
        fs::create_dir_all(&node_modules_dir)
            .context("Failed to create node_modules directory")?;

        // Filter out already installed packages
        let to_install: Vec<_> = packages
            .iter()
            .filter(|pkg| {
                let pkg_dir = node_modules_dir.join(&pkg.name);
                if pkg_dir.exists() {
                    ui::show_package_status(&pkg.name, &pkg.version, PackageStatus::Skipped);
                    false
                } else {
                    true
                }
            })
            .collect();

        if to_install.is_empty() {
            ui::show_info("All packages are already installed");
            return Ok(());
        }

        ui::show_info(&format!("📥 Downloading {} packages...", to_install.len()));
        
        // Create progress bar for installations
        let progress = ui::create_install_progress(to_install.len() as u64);

        // Install packages in parallel batches
        let batch_size = 10;
        let mut installed_count = 0;

        for chunk in to_install.chunks(batch_size) {
            let tasks: Vec<_> = chunk
                .iter()
                .map(|pkg| self.install_single_package(pkg, &node_modules_dir))
                .collect();

            let results = futures::future::join_all(tasks).await;
            
            for (pkg, result) in chunk.iter().zip(results.iter()) {
                match result {
                    Ok(_) => {
                        ui::show_package_status(&pkg.name, &pkg.version, PackageStatus::Installed);
                        installed_count += 1;
                    }
                    Err(e) => {
                        ui::show_package_status(&pkg.name, &pkg.version, PackageStatus::Failed);
                        eprintln!("Failed to install {}: {}", pkg.name, e);
                    }
                }
                progress.inc(1);
            }
        }

        progress.finish_with_message(format!("Installed {} packages", installed_count));

        // Update package.json if not global
        if !options.global {
            self.update_package_json(packages, options).await?;
        }

        let duration = start_time.elapsed();
        ui::show_timing("Installation", duration);
        
        ui::show_resolution_summary(packages.len(), installed_count, 0, packages.len() - installed_count);

        Ok(())
    }

    /// Install a single package with caching and extraction
    async fn install_single_package(
        &self,
        package: &ResolvedPackage,
        node_modules_dir: &Path,
    ) -> Result<()> {
        let _permit = self.semaphore.acquire().await?;
        
        let package_key = format!("{}@{}", package.name, package.version);
        
        // Check if already processed in this session
        {
            let installed = self.installed_packages.lock().unwrap();
            if installed.contains(&package_key) {
                return Ok(());
            }
        }

        // Check if package directory already exists
        let package_dir = node_modules_dir.join(&package.name);
        if package_dir.exists() {
            // Verify it's the correct version
            if self.verify_installed_version(&package_dir, &package.version)? {
                self.installed_packages.lock().unwrap().insert(package_key);
                return Ok(());
            } else {
                // Remove old version
                fs::remove_dir_all(&package_dir)
                    .context("Failed to remove old package version")?;
            }
        }

        // Extract package
        self.extractor
            .extract_package(
                &package.tarball_url,
                &package.name,
                &package.version,
                node_modules_dir,
            )
            .await
            .context("Failed to extract package")?;

        // Verify extraction
        self.extractor.verify_extraction(&package_dir)?;

        // Mark as installed
        self.installed_packages.lock().unwrap().insert(package_key);

        Ok(())
    }

    /// Verify if the installed package matches the expected version
    fn verify_installed_version(&self, package_dir: &Path, expected_version: &str) -> Result<bool> {
        let package_json_path = package_dir.join("package.json");
        if !package_json_path.exists() {
            return Ok(false);
        }

        let content = fs::read_to_string(&package_json_path)?;
        let package_json: serde_json::Value = serde_json::from_str(&content)?;
        
        if let Some(version) = package_json["version"].as_str() {
            Ok(version == expected_version)
        } else {
            Ok(false)
        }
    }

    /// Get the appropriate node_modules directory
    fn get_node_modules_dir(&self, global: bool) -> Result<PathBuf> {
        if global {
            let home_dir = dirs::home_dir()
                .ok_or_else(|| NxError::filesystem("Could not find home directory"))?;
            Ok(home_dir.join(".nx").join("global").join("node_modules"))
        } else {
            Ok(std::env::current_dir()?.join("node_modules"))
        }
    }

    /// Show dry run summary
    fn show_dry_run_summary(&self, packages: &[ResolvedPackage]) {
        ui::show_info(&format!("🔍 Would install {} packages (dry run)", packages.len()));
        for package in packages {
            println!("  - {}@{}", package.name, package.version);
        }
    }

    /// Update package.json with installed packages
    async fn update_package_json(
        &self,
        packages: &[ResolvedPackage],
        options: &InstallOptions,
    ) -> Result<()> {
        let package_json_path = Path::new("package.json");
        
        // Read existing package.json or create new one
        let mut package_json = if package_json_path.exists() {
            let content = fs::read_to_string(package_json_path)?;
            serde_json::from_str(&content)?
        } else {
            serde_json::json!({
                "name": "my-project",
                "version": "1.0.0",
                "description": "",
                "dependencies": {},
                "devDependencies": {}
            })
        };

        // Add packages to appropriate section
        let section = if options.save_dev { "devDependencies" } else { "dependencies" };
        
        if package_json[section].is_null() {
            package_json[section] = serde_json::json!({});
        }

        for package in packages {
            if let Some(deps) = package_json[section].as_object_mut() {
                deps.insert(package.name.clone(), serde_json::Value::String(
                    format!("^{}", package.version)
                ));
            }
        }

        // Write back to file
        let content = serde_json::to_string_pretty(&package_json)?;
        fs::write(package_json_path, content)?;

        Ok(())
    }

    /// List installed packages
    pub fn list_installed_packages(&self, global: bool) -> Result<Vec<InstalledPackage>> {
        let node_modules_dir = self.get_node_modules_dir(global)?;
        
        if !node_modules_dir.exists() {
            return Ok(Vec::new());
        }

        let mut packages = Vec::new();
        
        for entry in fs::read_dir(&node_modules_dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_dir() {
                let package_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string();
                
                // Skip hidden directories and .bin
                if package_name.starts_with('.') {
                    continue;
                }
                
                // Get version from package.json
                let package_json_path = path.join("package.json");
                if let Ok(content) = fs::read_to_string(&package_json_path) {
                    if let Ok(package_json) = serde_json::from_str::<serde_json::Value>(&content) {
                        if let Some(version) = package_json["version"].as_str() {
                            packages.push(InstalledPackage {
                                name: package_name,
                                version: version.to_string(),
                            });
                        }
                    }
                }
            }
        }
        
        packages.sort_by(|a, b| a.name.cmp(&b.name));
        Ok(packages)
    }

    /// Uninstall a package
    pub async fn uninstall_package(&self, package_name: &str, global: bool) -> Result<()> {
        let node_modules_dir = self.get_node_modules_dir(global)?;
        let package_dir = node_modules_dir.join(package_name);
        
        if !package_dir.exists() {
            return Err(NxError::validation(format!(
                "Package '{}' is not installed",
                package_name
            )).into());
        }

        fs::remove_dir_all(&package_dir)
            .context("Failed to remove package directory")?;

        // Remove from package.json if not global
        if !global {
            self.remove_from_package_json(package_name).await?;
        }

        Ok(())
    }

    /// Remove package from package.json
    async fn remove_from_package_json(&self, package_name: &str) -> Result<()> {
        let package_json_path = Path::new("package.json");
        if !package_json_path.exists() {
            return Ok(());
        }

        let content = fs::read_to_string(package_json_path)?;
        let mut package_json: serde_json::Value = serde_json::from_str(&content)?;

        // Remove from both dependencies and devDependencies
        if let Some(deps) = package_json["dependencies"].as_object_mut() {
            deps.remove(package_name);
        }
        if let Some(dev_deps) = package_json["devDependencies"].as_object_mut() {
            dev_deps.remove(package_name);
        }

        // Write back to file
        let content = serde_json::to_string_pretty(&package_json)?;
        fs::write(package_json_path, content)?;

        Ok(())
    }
}