//! Logout command implementation for registry authentication
//! 
//! This module handles user logout from npm registries.

use crate::commands::config::{remove_auth_token, get_registry_url};
use crate::commands::login::{get_current_user, is_logged_in};
use crate::ui;
use anyhow::Result;
use colored::*;
use std::time::Instant;

pub async fn handle_logout(cmd: crate::LogoutCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    let registry_url = if let Some(registry) = cmd.registry {
        registry
    } else {
        get_registry_url().await?
    };
    
    let registry_name = extract_registry_name(&registry_url);
    
    if verbose {
        println!("{} Logging out from {}...", 
            "info".cyan().bold(),
            registry_name.bright_white()
        );
    }
    
    // Check if user is currently logged in
    if !is_logged_in(Some(&registry_name)).await {
        ui::show_info(&format!("Not logged in to {}", registry_name));
        return Ok(());
    }
    
    // Get current user before logout
    let current_user = get_current_user(Some(&registry_name)).await;
    
    // Remove auth token
    remove_auth_token(&registry_name).await?;
    
    let elapsed = start_time.elapsed();
    
    if let Some(username) = current_user {
        ui::show_success(&format!(
            "Successfully logged out {} from {} in {:.2}s",
            username.bright_white(),
            registry_name.bright_cyan(),
            elapsed.as_secs_f64()
        ));
    } else {
        ui::show_success(&format!(
            "Successfully logged out from {} in {:.2}s",
            registry_name.bright_cyan(),
            elapsed.as_secs_f64()
        ));
    }
    
    // Show additional cleanup steps
    if verbose {
        show_cleanup_info(&registry_name);
    }
    
    Ok(())
}

fn extract_registry_name(registry_url: &str) -> String {
    if registry_url.contains("registry.npmjs.org") {
        "npm".to_string()
    } else if let Ok(url) = url::Url::parse(registry_url) {
        url.host_str().unwrap_or("registry").to_string()
    } else {
        "registry".to_string()
    }
}

fn show_cleanup_info(registry_name: &str) {
    println!();
    println!("{}", "🔒 Security Cleanup".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    println!();
    
    println!("  {} Auth token removed from local configuration", "✓".bright_green());
    println!("  {} Session invalidated for {}", "✓".bright_green(), registry_name.bright_white());
    
    println!();
    println!("{}", "💡 Next Steps".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    
    println!("  {} To login again:", "1.".bright_white().bold());
    println!("     {} {}", "nx".bright_cyan(), "login".bright_green());
    
    println!();
    println!("  {} To publish packages, you'll need to login first:", "2.".bright_white().bold());
    println!("     {} {} && {} {}", 
        "nx".bright_cyan(), "login".bright_green(),
        "nx".bright_cyan(), "publish".bright_green()
    );
    
    if registry_name != "npm" {
        println!();
        println!("  {} To login to a specific registry:", "3.".bright_white().bold());
        println!("     {} {} {}", 
            "nx".bright_cyan(), 
            "login".bright_green(),
            format!("--registry {}", registry_name).bright_white()
        );
    }
    
    println!();
}

// Helper function to logout from all registries
pub async fn logout_all(verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if verbose {
        println!("{} Logging out from all registries...", "info".cyan().bold());
    }
    
    // Common registries to check
    let registries = ["npm", "registry"];
    let mut logout_count = 0;
    
    for registry in &registries {
        if is_logged_in(Some(registry)).await {
            let current_user = get_current_user(Some(registry)).await;
            remove_auth_token(registry).await?;
            
            if verbose {
                if let Some(username) = current_user {
                    println!("{} Logged out {} from {}", 
                        "✓".bright_green(),
                        username.bright_white(),
                        registry.bright_cyan()
                    );
                } else {
                    println!("{} Logged out from {}", 
                        "✓".bright_green(),
                        registry.bright_cyan()
                    );
                }
            }
            
            logout_count += 1;
        }
    }
    
    let elapsed = start_time.elapsed();
    
    if logout_count > 0 {
        ui::show_success(&format!(
            "Successfully logged out from {} registries in {:.2}s",
            logout_count,
            elapsed.as_secs_f64()
        ));
    } else {
        ui::show_info("Not logged in to any registries");
    }
    
    Ok(())
}

// Helper function to show current login status
pub async fn show_login_status(verbose: bool) -> Result<()> {
    println!();
    println!("{}", "🔐 Login Status".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    
    let registries = ["npm", "registry"];
    let mut any_logged_in = false;
    
    for registry in &registries {
        if is_logged_in(Some(registry)).await {
            if let Some(username) = get_current_user(Some(registry)).await {
                println!("  {} {} - logged in as {}", 
                    "✓".bright_green(),
                    registry.bright_cyan(),
                    username.bright_white()
                );
                any_logged_in = true;
            } else {
                println!("  {} {} - authenticated (username unknown)", 
                    "✓".bright_green(),
                    registry.bright_cyan()
                );
                any_logged_in = true;
            }
        } else {
            println!("  {} {} - not logged in", 
                "✗".bright_red(),
                registry.bright_cyan()
            );
        }
    }
    
    if !any_logged_in {
        println!();
        println!("{} Use {} to authenticate", 
            "💡".bright_yellow(),
            "nx login".bright_cyan()
        );
    }
    
    println!();
    
    Ok(())
}
