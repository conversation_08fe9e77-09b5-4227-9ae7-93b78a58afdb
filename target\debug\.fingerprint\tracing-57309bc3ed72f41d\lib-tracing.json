{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 7161640089466910437, "path": 17935162546624088887, "deps": [[1906322745568073236, "pin_project_lite", false, 11564342335118793534], [3424551429995674438, "tracing_core", false, 18309912056012716241]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-57309bc3ed72f41d\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}