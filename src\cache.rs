use crate::errors::{NxError, NxResult};
use crate::types::{PackageMetadata, CacheInfo};
use anyhow::{Context, Result};
use blake3::Hasher;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::fs as async_fs;

#[derive(Debug, Clone, Serialize, Deserialize)]
struct CacheEntry {
    pub data: serde_json::Value,
    pub timestamp: u64,
    pub etag: Option<String>,
    pub expires: Option<u64>,
}

pub struct PackageCache {
    cache_dir: PathBuf,
    metadata_dir: PathBuf,
    tarballs_dir: PathBuf,
    max_age: u64, // seconds
}

impl PackageCache {
    pub fn new() -> Result<Self> {
        let home_dir = dirs::home_dir()
            .ok_or_else(|| NxError::filesystem("Could not find home directory"))?;
        let cache_dir = home_dir.join(".nx").join("cache");
        
        let metadata_dir = cache_dir.join("metadata");
        let tarballs_dir = cache_dir.join("tarballs");
        
        // Create cache directories
        fs::create_dir_all(&metadata_dir)?;
        fs::create_dir_all(&tarballs_dir)?;
        
        Ok(Self {
            cache_dir,
            metadata_dir,
            tarballs_dir,
            max_age: 3600, // 1 hour default
        })
    }
    
    pub fn with_max_age(mut self, max_age: u64) -> Self {
        self.max_age = max_age;
        self
    }
    
    /// Get package metadata from cache
    pub async fn get_metadata(&self, package_name: &str) -> NxResult<Option<PackageMetadata>> {
        let cache_path = self.metadata_path(package_name);
        
        if !cache_path.exists() {
            return Ok(None);
        }
        
        let content = async_fs::read_to_string(&cache_path).await
            .context("Failed to read cache file")?;
            
        let entry: CacheEntry = serde_json::from_str(&content)
            .context("Failed to parse cache entry")?;
        
        // Check if cache entry is still valid
        let now = current_timestamp();
        if now > entry.timestamp + self.max_age {
            // Cache expired, remove it
            let _ = async_fs::remove_file(&cache_path).await;
            return Ok(None);
        }
        
        let metadata: PackageMetadata = serde_json::from_value(entry.data)
            .context("Failed to deserialize package metadata")?;
            
        Ok(Some(metadata))
    }
    
    /// Store package metadata in cache
    pub async fn set_metadata(&self, package_name: &str, metadata: &PackageMetadata, etag: Option<String>) -> NxResult<()> {
        let cache_path = self.metadata_path(package_name);
        
        // Create parent directory if it doesn't exist
        if let Some(parent) = cache_path.parent() {
            async_fs::create_dir_all(parent).await
                .context("Failed to create cache directory")?;
        }
        
        let entry = CacheEntry {
            data: serde_json::to_value(metadata)
                .context("Failed to serialize metadata")?,
            timestamp: current_timestamp(),
            etag,
            expires: None,
        };
        
        let content = serde_json::to_string_pretty(&entry)
            .context("Failed to serialize cache entry")?;
            
        async_fs::write(&cache_path, content).await
            .context("Failed to write cache file")?;
            
        Ok(())
    }
    
    /// Get cached tarball path
    pub fn get_tarball_path(&self, package_name: &str, version: &str) -> Option<PathBuf> {
        let filename = format!("{}-{}.tgz", package_name, version);
        let tarball_path = self.tarballs_dir.join(&filename);
        
        if tarball_path.exists() {
            Some(tarball_path)
        } else {
            None
        }
    }
    
    /// Store tarball in cache
    pub async fn store_tarball(&self, package_name: &str, version: &str, data: &[u8]) -> NxResult<PathBuf> {
        let filename = format!("{}-{}.tgz", package_name, version);
        let tarball_path = self.tarballs_dir.join(&filename);
        
        async_fs::write(&tarball_path, data).await
            .context("Failed to write tarball to cache")?;
            
        Ok(tarball_path)
    }
    
    /// Verify tarball integrity
    pub async fn verify_tarball(&self, path: &Path, expected_integrity: &str) -> NxResult<bool> {
        let data = async_fs::read(path).await
            .context("Failed to read tarball")?;
            
        let actual_hash = self.calculate_integrity(&data);
        Ok(actual_hash == expected_integrity)
    }
    
    /// Calculate integrity hash (using SHA-512 like npm)
    pub fn calculate_integrity(&self, data: &[u8]) -> String {
        let mut hasher = Hasher::new();
        hasher.update(data);
        let hash = hasher.finalize();
        format!("sha512-{}", base64::encode(hash.as_bytes()))
    }
    
    /// Clean expired cache entries
    pub async fn clean_expired(&self) -> NxResult<usize> {
        let mut cleaned = 0;
        let now = current_timestamp();
        
        // Clean metadata cache
        cleaned += self.clean_directory(&self.metadata_dir, now).await?;
        
        Ok(cleaned)
    }
    
    /// Clean all cache
    pub async fn clean_all(&self) -> NxResult<()> {
        if self.cache_dir.exists() {
            async_fs::remove_dir_all(&self.cache_dir).await
                .context("Failed to remove cache directory")?;
        }
        
        // Recreate cache directories
        async_fs::create_dir_all(&self.metadata_dir).await?;
        async_fs::create_dir_all(&self.tarballs_dir).await?;
        
        Ok(())
    }
    
    /// Get cache statistics
    pub async fn get_cache_info(&self) -> NxResult<CacheInfo> {
        let mut size = 0u64;
        let mut package_count = 0usize;
        let mut last_updated: Option<String> = None;
        
        // Calculate metadata cache size
        if self.metadata_dir.exists() {
            let mut entries = async_fs::read_dir(&self.metadata_dir).await?;
            while let Some(entry) = entries.next_entry().await? {
                if let Ok(metadata) = entry.metadata().await {
                    size += metadata.len();
                    package_count += 1;
                    
                    if let Ok(modified) = metadata.modified() {
                        let timestamp = modified.duration_since(UNIX_EPOCH)
                            .unwrap_or_default().as_secs();
                        let formatted = format_timestamp(timestamp);
                        
                        if last_updated.as_ref().map_or(true, |lu| formatted > *lu) {
                            last_updated = Some(formatted);
                        }
                    }
                }
            }
        }
        
        // Calculate tarballs cache size
        if self.tarballs_dir.exists() {
            let mut entries = async_fs::read_dir(&self.tarballs_dir).await?;
            while let Some(entry) = entries.next_entry().await? {
                if let Ok(metadata) = entry.metadata().await {
                    size += metadata.len();
                }
            }
        }
        
        Ok(CacheInfo {
            size,
            package_count,
            last_updated,
        })
    }
    
    /// Clean a directory of expired entries
    async fn clean_directory(&self, dir: &Path, now: u64) -> NxResult<usize> {
        let mut cleaned = 0;
        
        if !dir.exists() {
            return Ok(0);
        }
        
        let mut entries = async_fs::read_dir(dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            
            if let Ok(content) = async_fs::read_to_string(&path).await {
                if let Ok(cache_entry) = serde_json::from_str::<CacheEntry>(&content) {
                    if now > cache_entry.timestamp + self.max_age {
                        let _ = async_fs::remove_file(&path).await;
                        cleaned += 1;
                    }
                }
            }
        }
        
        Ok(cleaned)
    }
    
    /// Get metadata cache file path
    fn metadata_path(&self, package_name: &str) -> PathBuf {
        // Create subdirectories based on package name to avoid too many files in one directory
        let mut hasher = Hasher::new();
        hasher.update(package_name.as_bytes());
        let hash = format!("{:x}", hasher.finalize());
        let subdir = &hash[0..2];
        
        self.metadata_dir
            .join(subdir)
            .join(format!("{}.json", package_name))
    }
}

fn current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

fn format_timestamp(timestamp: u64) -> String {
    use std::time::{Duration, UNIX_EPOCH};
    let datetime = UNIX_EPOCH + Duration::from_secs(timestamp);
    format!("{:?}", datetime) // Simple formatting, could use chrono for better formatting
}
