{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 18350989681977977651, "path": 4399355794312995255, "deps": [[5820056977320921005, "anstream", false, 14268392907399180611], [9394696648929125047, "anstyle", false, 6419716995771230463], [11166530783118767604, "strsim", false, 8020763692596646254], [11649982696571033535, "clap_lex", false, 13414364529549192955]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-454e748cf1fb1b1d\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}