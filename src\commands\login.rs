//! Login command implementation for registry authentication
//! 
//! This module handles user authentication with npm registries.

use crate::commands::config::{set_auth_token, get_registry_url};
use crate::ui;
use anyhow::{Result, Context};
use colored::*;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::io::{self, Write};
use std::time::Instant;

#[derive(Debug, Serialize)]
struct LoginRequest {
    name: String,
    password: String,
}

#[derive(Debug, Deserialize)]
struct LoginResponse {
    token: String,
    key: Option<String>,
    cidr_whitelist: Option<Vec<String>>,
    readonly: Option<bool>,
    automation: Option<bool>,
}

#[derive(Debug, Deserialize)]
struct ErrorResponse {
    error: String,
}

pub async fn handle_login(cmd: crate::LoginCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    let registry_url = if let Some(registry) = cmd.registry {
        registry
    } else {
        get_registry_url().await?
    };
    
    if verbose {
        println!("{} Logging in to {}...", 
            "info".cyan().bold(),
            registry_url.bright_white()
        );
    }
    
    // Get username and password
    let username = prompt_username()?;
    let password = prompt_password()?;
    
    // Perform login
    let token = perform_login(&registry_url, &username, &password, verbose).await?;
    
    // Store auth token
    let registry_name = extract_registry_name(&registry_url);
    set_auth_token(&registry_name, &token).await?;
    
    let elapsed = start_time.elapsed();
    
    ui::show_success(&format!(
        "Successfully logged in to {} as {} in {:.2}s",
        registry_name.bright_cyan(),
        username.bright_white(),
        elapsed.as_secs_f64()
    ));
    
    // Verify login by making a test request
    verify_login(&registry_url, &token, verbose).await?;
    
    Ok(())
}

fn prompt_username() -> Result<String> {
    print!("{} ", "Username:".bright_cyan());
    io::stdout().flush()?;
    
    let mut username = String::new();
    io::stdin().read_line(&mut username)?;
    let username = username.trim().to_string();
    
    if username.is_empty() {
        return Err(anyhow::anyhow!("Username cannot be empty"));
    }
    
    Ok(username)
}

fn prompt_password() -> Result<String> {
    print!("{} ", "Password:".bright_cyan());
    io::stdout().flush()?;
    
    // Use rpassword crate for hidden password input in production
    // For now, use simple input (not recommended for production)
    let mut password = String::new();
    io::stdin().read_line(&mut password)?;
    let password = password.trim().to_string();
    
    if password.is_empty() {
        return Err(anyhow::anyhow!("Password cannot be empty"));
    }
    
    Ok(password)
}

async fn perform_login(
    registry_url: &str,
    username: &str,
    password: &str,
    verbose: bool,
) -> Result<String> {
    let client = Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .context("Failed to create HTTP client")?;
    
    let login_url = format!("{}/-/user/org.couchdb.user:{}", 
        registry_url.trim_end_matches('/'), 
        username
    );
    
    let login_request = LoginRequest {
        name: username.to_string(),
        password: password.to_string(),
    };
    
    if verbose {
        println!("{} Authenticating with registry...", "info".cyan());
    }
    
    let spinner = ui::create_resolution_spinner();
    spinner.set_message("Authenticating...");
    
    let response = client
        .put(&login_url)
        .header("User-Agent", "nx/0.2.0")
        .header("Content-Type", "application/json")
        .json(&login_request)
        .send()
        .await
        .context("Failed to send login request")?;
    
    spinner.finish_and_clear();
    
    if response.status().is_success() {
        let login_response: LoginResponse = response
            .json()
            .await
            .context("Failed to parse login response")?;
        
        Ok(login_response.token)
    } else {
        let error_text = response.text().await.unwrap_or_default();
        
        // Try to parse as error response
        if let Ok(error_response) = serde_json::from_str::<ErrorResponse>(&error_text) {
            return Err(anyhow::anyhow!("Login failed: {}", error_response.error));
        }
        
        // Fallback to HTTP status
        match response.status().as_u16() {
            401 => Err(anyhow::anyhow!("Login failed: Invalid username or password")),
            403 => Err(anyhow::anyhow!("Login failed: Access forbidden")),
            404 => Err(anyhow::anyhow!("Login failed: Registry not found")),
            _ => Err(anyhow::anyhow!("Login failed: HTTP {}", response.status())),
        }
    }
}

async fn verify_login(registry_url: &str, token: &str, verbose: bool) -> Result<()> {
    let client = Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .context("Failed to create HTTP client")?;
    
    let whoami_url = format!("{}/-/whoami", registry_url.trim_end_matches('/'));
    
    if verbose {
        println!("{} Verifying authentication...", "info".cyan());
    }
    
    let response = client
        .get(&whoami_url)
        .header("User-Agent", "nx/0.2.0")
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await
        .context("Failed to verify login")?;
    
    if response.status().is_success() {
        if let Ok(whoami_response) = response.json::<serde_json::Value>().await {
            if let Some(username) = whoami_response.get("username").and_then(|u| u.as_str()) {
                if verbose {
                    println!("{} Authenticated as {}", 
                        "✓".bright_green(), 
                        username.bright_white()
                    );
                }
            }
        }
    } else {
        ui::show_warning("Authentication token stored, but verification failed");
    }
    
    Ok(())
}

fn extract_registry_name(registry_url: &str) -> String {
    if registry_url.contains("registry.npmjs.org") {
        "npm".to_string()
    } else if let Ok(url) = url::Url::parse(registry_url) {
        url.host_str().unwrap_or("registry").to_string()
    } else {
        "registry".to_string()
    }
}

// Alternative login method using auth tokens directly
pub async fn handle_token_login(registry: Option<String>, token: String, verbose: bool) -> Result<()> {
    let registry_url = if let Some(reg) = registry {
        reg
    } else {
        get_registry_url().await?
    };
    
    let registry_name = extract_registry_name(&registry_url);
    
    // Verify token works
    if let Err(e) = verify_login(&registry_url, &token, verbose).await {
        ui::show_warning(&format!("Token verification failed: {}", e));
        ui::show_info("Token will be stored anyway. You can test it later.");
    }
    
    // Store the token
    set_auth_token(&registry_name, &token).await?;
    
    ui::show_success(&format!(
        "Auth token stored for {}",
        registry_name.bright_cyan()
    ));
    
    Ok(())
}

// Helper function to check if user is logged in
pub async fn is_logged_in(registry: Option<&str>) -> bool {
    let registry_name = registry.unwrap_or("npm");
    
    if let Some(token) = crate::commands::config::get_auth_token(registry_name).await {
        if let Ok(registry_url) = get_registry_url().await {
            return verify_login(&registry_url, &token, false).await.is_ok();
        }
    }
    
    false
}

// Helper function to get current logged in user
pub async fn get_current_user(registry: Option<&str>) -> Option<String> {
    let registry_name = registry.unwrap_or("npm");
    
    if let Some(token) = crate::commands::config::get_auth_token(registry_name).await {
        if let Ok(registry_url) = get_registry_url().await {
            if let Ok(client) = Client::builder()
                .timeout(std::time::Duration::from_secs(10))
                .build()
            {
                let whoami_url = format!("{}/-/whoami", registry_url.trim_end_matches('/'));
                
                if let Ok(response) = client
                    .get(&whoami_url)
                    .header("User-Agent", "nx/0.2.0")
                    .header("Authorization", format!("Bearer {}", token))
                    .send()
                    .await
                {
                    if let Ok(whoami_response) = response.json::<serde_json::Value>().await {
                        return whoami_response
                            .get("username")
                            .and_then(|u| u.as_str())
                            .map(|s| s.to_string());
                    }
                }
            }
        }
    }
    
    None
}
