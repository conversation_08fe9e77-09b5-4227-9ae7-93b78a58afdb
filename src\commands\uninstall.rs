//! Uninstall command implementation
//! 
//! This module handles uninstalling packages with dependency cleanup.

use crate::installer::PackageInstaller;
use crate::ui;
use anyhow::Result;

pub async fn handle_uninstall(packages: Vec<String>, global: bool) -> Result<()> {
    if packages.is_empty() {
        ui::show_error("No packages specified for uninstall");
        return Ok(());
    }
    
    ui::show_info(&format!("🗑️ Uninstalling {} package(s)...", packages.len()));
    
    let installer = PackageInstaller::new()?;
    
    for package_name in &packages {
        ui::show_info(&format!("🗑️ Removing {}...", package_name));
        
        match installer.uninstall_package(package_name, global).await {
            Ok(_) => ui::show_success(&format!("Removed {}", package_name)),
            Err(e) => ui::show_error(&format!("Failed to remove {}: {}", package_name, e)),
        }
    }
    
    ui::show_success("Uninstall completed successfully!");
    
    Ok(())
}
