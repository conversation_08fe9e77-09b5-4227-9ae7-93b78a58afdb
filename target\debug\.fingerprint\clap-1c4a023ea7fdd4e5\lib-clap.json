{"rustc": 1842507548689473721, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 18350989681977977651, "path": 16478602202383672790, "deps": [[1457576002496728321, "clap_derive", false, 13150806781359660534], [12790452388100355468, "clap_builder", false, 15877778269959167933]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-1c4a023ea7fdd4e5\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}