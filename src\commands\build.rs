//! Build command implementation for project building
//! 
//! This module handles building projects with various build tools.

use crate::commands::run::run_lifecycle_scripts;
use crate::types::PackageJson;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::path::Path;
use std::time::Instant;

pub async fn handle_build(cmd: crate::BuildCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    let package_json_path = Path::new("package.json");
    
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    if verbose {
        println!("{} Building project...", "info".cyan().bold());
        
        if cmd.production {
            println!("{} Building for production", "info".cyan());
        }
        
        if cmd.watch {
            println!("{} Building in watch mode", "info".cyan());
        }
    }
    
    // Set environment variables
    if cmd.production {
        std::env::set_var("NODE_ENV", "production");
    } else {
        std::env::set_var("NODE_ENV", "development");
    }
    
    // Try different build strategies
    build_project(&package_json, &cmd, verbose).await?;
    
    let elapsed = start_time.elapsed();
    ui::show_success(&format!(
        "Build completed in {:.2}s",
        elapsed.as_secs_f64()
    ));
    
    Ok(())
}

async fn build_project(
    package_json: &PackageJson,
    cmd: &crate::BuildCommand,
    verbose: bool,
) -> Result<()> {
    // First, try to run the build script from package.json
    if let Some(ref scripts) = package_json.scripts {
        let script_name = if cmd.production && scripts.contains_key("build:prod") {
            "build:prod"
        } else if !cmd.production && scripts.contains_key("build:dev") {
            "build:dev"
        } else if cmd.watch && scripts.contains_key("build:watch") {
            "build:watch"
        } else if scripts.contains_key("build") {
            "build"
        } else {
            ""
        };
        
        if !script_name.is_empty() {
            if verbose {
                let script_command = scripts.get(script_name).unwrap();
                println!("{} Running {} script from package.json", "info".cyan(), script_name.bright_white());
                println!("{} Command: {}", "info".cyan(), script_command.bright_white());
            }
            
            run_lifecycle_scripts(script_name, package_json, verbose).await?;
            return Ok(());
        }
    }
    
    // If no build script, try to detect build tool and suggest build method
    detect_and_suggest_build(package_json, cmd, verbose).await?;
    
    Ok(())
}

async fn detect_and_suggest_build(
    package_json: &PackageJson,
    cmd: &crate::BuildCommand,
    verbose: bool,
) -> Result<()> {
    if verbose {
        println!("{} Detecting build tools...", "info".cyan());
    }
    
    let mut detected_tools = Vec::new();
    let mut suggested_commands = Vec::new();
    
    // Check for TypeScript
    if Path::new("tsconfig.json").exists() {
        detected_tools.push("TypeScript");
        suggested_commands.push(("tsc", "TypeScript compilation"));
    }
    
    // Check for common build tools in dev dependencies
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        if dev_deps.contains_key("vite") {
            detected_tools.push("Vite");
            suggested_commands.push(("vite build", "Vite build"));
        }
        
        if dev_deps.contains_key("webpack") {
            detected_tools.push("Webpack");
            suggested_commands.push(("webpack", "Webpack build"));
        }
        
        if dev_deps.contains_key("parcel") {
            detected_tools.push("Parcel");
            suggested_commands.push(("parcel build", "Parcel build"));
        }
        
        if dev_deps.contains_key("rollup") {
            detected_tools.push("Rollup");
            suggested_commands.push(("rollup -c", "Rollup build"));
        }
        
        if dev_deps.contains_key("esbuild") {
            detected_tools.push("ESBuild");
            suggested_commands.push(("esbuild", "ESBuild"));
        }
        
        if dev_deps.contains_key("@angular/cli") {
            detected_tools.push("Angular CLI");
            suggested_commands.push(("ng build", "Angular build"));
        }
        
        if dev_deps.contains_key("@vue/cli-service") {
            detected_tools.push("Vue CLI");
            suggested_commands.push(("vue-cli-service build", "Vue CLI build"));
        }
    }
    
    // Check for framework-specific builds
    if let Some(ref deps) = package_json.dependencies {
        if deps.contains_key("next") {
            detected_tools.push("Next.js");
            suggested_commands.push(("next build", "Next.js build"));
        }
        
        if deps.contains_key("nuxt") {
            detected_tools.push("Nuxt.js");
            suggested_commands.push(("nuxt build", "Nuxt.js build"));
        }
        
        if deps.contains_key("gatsby") {
            detected_tools.push("Gatsby");
            suggested_commands.push(("gatsby build", "Gatsby build"));
        }
    }
    
    // Check for configuration files
    let config_files = [
        ("vite.config.js", "Vite", "vite build"),
        ("vite.config.ts", "Vite", "vite build"),
        ("webpack.config.js", "Webpack", "webpack --mode production"),
        ("webpack.config.ts", "Webpack", "webpack --mode production"),
        ("rollup.config.js", "Rollup", "rollup -c"),
        ("rollup.config.ts", "Rollup", "rollup -c"),
        ("angular.json", "Angular CLI", "ng build"),
        ("vue.config.js", "Vue CLI", "vue-cli-service build"),
        ("next.config.js", "Next.js", "next build"),
        ("nuxt.config.js", "Nuxt.js", "nuxt build"),
        ("gatsby-config.js", "Gatsby", "gatsby build"),
        ("svelte.config.js", "SvelteKit", "svelte-kit build"),
    ];
    
    for (config_file, tool_name, command) in &config_files {
        if Path::new(config_file).exists() {
            if !detected_tools.contains(tool_name) {
                detected_tools.push(tool_name);
                suggested_commands.push((command, &format!("{} build", tool_name)));
            }
        }
    }
    
    if !detected_tools.is_empty() {
        println!("{} Detected build tools: {}", 
            "✓".bright_green(),
            detected_tools.join(", ").bright_white()
        );
    }
    
    // Provide suggestions
    ui::show_warning("No build script found in package.json");
    
    println!();
    println!("{}", "💡 Suggested solutions:".bright_cyan().bold());
    println!();
    
    println!("  {} Add a build script to package.json:", "1.".bright_white().bold());
    
    if !suggested_commands.is_empty() {
        for (command, description) in &suggested_commands {
            println!("     {} {} ({})", 
                format!(r#""build": "{}""#, command).dimmed(),
                "←".dimmed(),
                description.dimmed()
            );
        }
    } else if Path::new("tsconfig.json").exists() {
        println!("     {}", r#""scripts": { "build": "tsc" }"#.dimmed());
    } else {
        println!("     {}", r#""scripts": { "build": "webpack --mode production" }"#.dimmed());
        println!("     {}", r#""scripts": { "build": "vite build" }"#.dimmed());
    }
    
    println!();
    println!("  {} Install a build tool:", "2.".bright_white().bold());
    
    if detected_tools.is_empty() {
        println!("     {} {} {}", "nx".bright_cyan(), "install".bright_green(), "--save-dev vite".bright_white());
        println!("     {} {} {}", "nx".bright_cyan(), "install".bright_green(), "--save-dev webpack webpack-cli".bright_white());
        println!("     {} {} {}", "nx".bright_cyan(), "install".bright_green(), "--save-dev typescript".bright_white());
    }
    
    println!();
    println!("  {} Common build script patterns:", "3.".bright_white().bold());
    println!("     {} Development build with watch mode", r#""build:dev": "webpack --mode development --watch""#.dimmed());
    println!("     {} Production build", r#""build:prod": "webpack --mode production""#.dimmed());
    println!("     {} TypeScript compilation", r#""build:ts": "tsc""#.dimmed());
    
    if cmd.production {
        println!();
        println!("{} Note: Building for production (NODE_ENV=production)", 
            "🏭".bright_blue()
        );
    }
    
    if cmd.watch {
        println!();
        println!("{} Note: Watch mode requested but no build:watch script found", 
            "👁️".bright_yellow()
        );
    }
    
    println!();
    
    Ok(())
}

// Helper function to check if build output exists
pub async fn check_build_output() -> Result<Vec<String>> {
    let common_build_dirs = [
        "dist/",
        "build/",
        "out/",
        "lib/",
        "public/",
        ".next/",
        ".nuxt/",
    ];
    
    let mut found_dirs = Vec::new();
    
    for dir in &common_build_dirs {
        let path = Path::new(dir);
        if path.exists() && path.is_dir() {
            found_dirs.push(dir.to_string());
        }
    }
    
    Ok(found_dirs)
}

// Helper function to clean build output
pub async fn clean_build_output(verbose: bool) -> Result<()> {
    let build_dirs = check_build_output().await?;
    
    if build_dirs.is_empty() {
        ui::show_info("No build output directories found");
        return Ok(());
    }
    
    if verbose {
        println!("{} Cleaning build output directories...", "info".cyan().bold());
    }
    
    for dir in &build_dirs {
        let path = Path::new(dir);
        if path.exists() {
            tokio::fs::remove_dir_all(path).await
                .with_context(|| format!("Failed to remove build directory: {}", dir))?;
            
            if verbose {
                println!("{} Removed {}", "✓".bright_green(), dir.bright_white());
            }
        }
    }
    
    ui::show_success(&format!("Cleaned {} build directories", build_dirs.len()));
    
    Ok(())
}
