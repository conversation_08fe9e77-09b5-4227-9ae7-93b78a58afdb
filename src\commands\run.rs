//! Run command implementation for executing package scripts
//! 
//! This module handles running scripts defined in package.json.

use crate::types::PackageJson;
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use std::path::Path;
use std::process::Stdio;
use std::time::Instant;
use tokio::process::Command;

pub async fn handle_run(cmd: crate::RunCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    // Read package.json to get available scripts
    let package_json_path = Path::new("package.json");
    
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    let scripts = package_json.scripts.as_ref();
    
    // If no script specified, list available scripts
    if cmd.script.is_empty() {
        list_available_scripts(scripts);
        return Ok(());
    }
    
    // Find the script to run
    let script_command = if let Some(scripts) = scripts {
        if let Some(command) = scripts.get(&cmd.script) {
            command.clone()
        } else {
            // Check for common script patterns
            match cmd.script.as_str() {
                "start" => {
                    if scripts.contains_key("start") {
                        scripts["start"].clone()
                    } else {
                        "node server.js".to_string()
                    }
                },
                "test" => {
                    if scripts.contains_key("test") {
                        scripts["test"].clone()
                    } else {
                        "echo \"Error: no test specified\" && exit 1".to_string()
                    }
                },
                "build" => {
                    if scripts.contains_key("build") {
                        scripts["build"].clone()
                    } else {
                        ui::show_error(&format!("Script '{}' not found in package.json", cmd.script));
                        return Ok(());
                    }
                },
                _ => {
                    ui::show_error(&format!("Script '{}' not found in package.json", cmd.script));
                    list_available_scripts(scripts);
                    return Ok(());
                }
            }
        }
    } else {
        ui::show_error("No scripts section found in package.json");
        return Ok(());
    };
    
    if verbose {
        println!("{} Running script '{}'...", 
            "info".cyan().bold(),
            cmd.script.bright_white()
        );
        println!("{} {}", "Command:".dimmed(), script_command.bright_white());
    }
    
    // Execute the script
    let exit_status = execute_script(&script_command, &cmd.args, verbose).await?;
    
    let elapsed = start_time.elapsed();
    
    if exit_status.success() {
        ui::show_success(&format!(
            "Script '{}' completed successfully in {:.2}s",
            cmd.script,
            elapsed.as_secs_f64()
        ));
    } else {
        let code = exit_status.code().unwrap_or(-1);
        ui::show_error(&format!(
            "Script '{}' failed with exit code {} after {:.2}s",
            cmd.script,
            code,
            elapsed.as_secs_f64()
        ));
        std::process::exit(code);
    }
    
    Ok(())
}

fn list_available_scripts(scripts: Option<&std::collections::HashMap<String, String>>) {
    if let Some(scripts) = scripts {
        if scripts.is_empty() {
            ui::show_info("No scripts defined in package.json");
            return;
        }
        
        println!();
        println!("{}", "Available scripts:".bright_cyan().bold());
        println!();
        
        let mut script_list: Vec<_> = scripts.iter().collect();
        script_list.sort_by_key(|(name, _)| *name);
        
        for (name, command) in script_list {
            println!("  {} {}",
                name.bright_green().bold(),
                format!("nx run {}", name).dimmed()
            );
            println!("    {}", command.white());
            println!();
        }
    } else {
        ui::show_info("No scripts section found in package.json");
    }
}

async fn execute_script(
    command: &str,
    args: &[String],
    verbose: bool,
) -> Result<std::process::ExitStatus> {
    // Parse the command to handle shell operators and pipes
    let full_command = if args.is_empty() {
        command.to_string()
    } else {
        format!("{} {}", command, args.join(" "))
    };
    
    if verbose {
        println!("{} Executing: {}", "info".cyan(), full_command.bright_white());
        println!("{}", "─".repeat(80).dimmed());
    }
    
    // Determine shell based on OS
    let (shell_cmd, shell_arg) = if cfg!(target_os = "windows") {
        ("cmd", "/C")
    } else {
        ("sh", "-c")
    };
    
    // Create and configure the command
    let status = Command::new(shell_cmd)
        .arg(shell_arg)
        .arg(&full_command)
        .stdin(Stdio::inherit())
        .stdout(Stdio::inherit())
        .stderr(Stdio::inherit())
        .status()
        .await
        .context("Failed to execute script")?;
    
    Ok(status)
}

// Helper function to handle pre/post script hooks
pub async fn run_lifecycle_scripts(
    script_name: &str,
    package_json: &PackageJson,
    verbose: bool,
) -> Result<()> {
    if let Some(ref scripts) = package_json.scripts {
        // Run pre-script if it exists
        let pre_script = format!("pre{}", script_name);
        if let Some(pre_command) = scripts.get(&pre_script) {
            if verbose {
                println!("{} Running pre-script '{}'", "info".cyan(), pre_script.bright_white());
            }
            
            let status = execute_script(pre_command, &[], verbose).await?;
            if !status.success() {
                ui::show_error(&format!("Pre-script '{}' failed", pre_script));
                return Ok(());
            }
        }
        
        // Run main script
        if let Some(main_command) = scripts.get(script_name) {
            let status = execute_script(main_command, &[], verbose).await?;
            if !status.success() {
                ui::show_error(&format!("Script '{}' failed", script_name));
                return Ok(());
            }
        }
        
        // Run post-script if it exists
        let post_script = format!("post{}", script_name);
        if let Some(post_command) = scripts.get(&post_script) {
            if verbose {
                println!("{} Running post-script '{}'", "info".cyan(), post_script.bright_white());
            }
            
            let status = execute_script(post_command, &[], verbose).await?;
            if !status.success() {
                ui::show_warning(&format!("Post-script '{}' failed", post_script));
            }
        }
    }
    
    Ok(())
}
