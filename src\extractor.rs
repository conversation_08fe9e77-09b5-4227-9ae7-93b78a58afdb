use crate::errors::{Nx<PERSON>rror, NxResult};
use anyhow::{Context, Result};
use flate2::read::GzDecoder;
use std::fs;
use std::path::{Path, PathBuf};
use tar::Archive;
use tokio::task;

pub struct TarballExtractor {
    temp_dir: PathBuf,
}

impl TarballExtractor {
    pub fn new() -> Self {
        Self {
            temp_dir: std::env::temp_dir().join("nx-extracts"),
        }
    }

    pub async fn extract_package(
        &self,
        tarball_url: &str,
        package_name: &str,
        version: &str,
        target_dir: &Path,
    ) -> NxResult<()> {
        let package_dir = target_dir.join(package_name);
        
        // Download tarball (simplified - would use HTTP client in real implementation)
        let response = reqwest::get(tarball_url).await?;
        let bytes = response.bytes().await?;
        
        // Extract in background task
        let bytes_vec = bytes.to_vec();
        let package_dir_clone = package_dir.clone();
        
        task::spawn_blocking(move || {
            let decoder = GzDecoder::new(&bytes_vec[..]);
            let mut archive = Archive::new(decoder);
            
            // Extract to temporary directory first
            let temp_extract = std::env::temp_dir().join(format!("nx-extract-{}", uuid::Uuid::new_v4()));
            fs::create_dir_all(&temp_extract)?;
            
            // Extract archive
            archive.unpack(&temp_extract)?;
            
            // Find the package directory (usually has "package" folder)
            let package_src = if temp_extract.join("package").exists() {
                temp_extract.join("package")
            } else {
                // Find first directory
                for entry in fs::read_dir(&temp_extract)? {
                    let entry = entry?;
                    if entry.file_type()?.is_dir() {
                        return Ok(entry.path());
                    }
                }
                return Err(NxError::extraction_failed("package", "No package directory found"));
            };
            
            // Move to final location
            if package_dir_clone.exists() {
                fs::remove_dir_all(&package_dir_clone)?;
            }
            fs::create_dir_all(package_dir_clone.parent().unwrap())?;
            fs::rename(&package_src, &package_dir_clone)?;
            
            // Cleanup temp directory
            let _ = fs::remove_dir_all(&temp_extract);
            
            Ok::<(), NxError>(())
        }).await
        .context("Failed to extract package")??;
        
        Ok(())
    }

    pub fn verify_extraction(&self, package_dir: &Path) -> NxResult<()> {
        if !package_dir.exists() {
            return Err(NxError::extraction_failed("package", "Package directory not found after extraction"));
        }
        
        let package_json = package_dir.join("package.json");
        if !package_json.exists() {
            return Err(NxError::extraction_failed("package", "package.json not found"));
        }
        
        Ok(())
    }
}
