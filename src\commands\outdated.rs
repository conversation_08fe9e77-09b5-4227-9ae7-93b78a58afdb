//! Outdated command implementation for checking package updates
//! 
//! This module handles checking for outdated packages and displaying update information.

use crate::types::{PackageJson, UpdateInfo, UpdateType};
use crate::ui;
use crate::utils;
use anyhow::{Result, Context};
use colored::*;
use reqwest::Client;
use semver::Version;
use serde_json::Value;
use std::collections::HashMap;
use std::path::Path;
use std::time::Instant;

pub async fn handle_outdated(cmd: crate::OutdatedCommand, verbose: bool) -> Result<()> {
    let start_time = Instant::now();
    
    if cmd.global {
        check_global_outdated(verbose).await?;
    } else {
        check_local_outdated(verbose).await?;
    }
    
    let elapsed = start_time.elapsed();
    if verbose {
        println!();
        println!("{} Outdated check completed in {:.2}s", 
            "✨".bright_yellow(),
            elapsed.as_secs_f64()
        );
    }
    
    Ok(())
}

async fn check_local_outdated(verbose: bool) -> Result<()> {
    let package_json_path = Path::new("package.json");
    
    if !package_json_path.exists() {
        ui::show_error("No package.json found in current directory");
        return Ok(());
    }
    
    let package_json = utils::read_package_json(Path::new(".")).await?
        .context("Failed to parse package.json")?;
    
    // Collect current dependencies
    let mut current_deps = HashMap::new();
    
    if let Some(ref deps) = package_json.dependencies {
        for (name, version) in deps {
            current_deps.insert(name.clone(), (version.clone(), "dependency".to_string()));
        }
    }
    
    if let Some(ref dev_deps) = package_json.dev_dependencies {
        for (name, version) in dev_deps {
            current_deps.insert(name.clone(), (version.clone(), "devDependency".to_string()));
        }
    }
    
    if let Some(ref optional_deps) = package_json.optional_dependencies {
        for (name, version) in optional_deps {
            current_deps.insert(name.clone(), (version.clone(), "optionalDependency".to_string()));
        }
    }
    
    if current_deps.is_empty() {
        ui::show_info("No dependencies found to check");
        return Ok(());
    }
    
    if verbose {
        println!("{} Checking {} packages for updates...", 
            "info".cyan().bold(),
            current_deps.len().to_string().bright_white().bold()
        );
    }
    
    // Check for outdated packages
    let outdated = check_outdated_packages(&current_deps, verbose).await?;
    
    if outdated.is_empty() {
        ui::show_success("All packages are up to date! 🎉");
        return Ok(());
    }
    
    // Display outdated packages
    display_outdated_table(&outdated, &current_deps);
    
    // Show update command
    show_update_suggestions(&outdated);
    
    Ok(())
}

async fn check_global_outdated(verbose: bool) -> Result<()> {
    let global_dir = utils::get_global_packages_dir()?;
    
    if !global_dir.exists() {
        ui::show_info("No global packages installed");
        return Ok(());
    }
    
    // Get list of global packages
    let mut global_packages = HashMap::new();
    let mut entries = tokio::fs::read_dir(&global_dir).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let name = entry.file_name().to_string_lossy().to_string();
        
        if name.starts_with('.') || name == "bin" {
            continue;
        }
        
        let package_json_path = entry.path().join("package.json");
        if package_json_path.exists() {
            let content = tokio::fs::read_to_string(&package_json_path).await?;
            if let Ok(pkg_json) = serde_json::from_str::<PackageJson>(&content) {
                if let Some(version) = pkg_json.version {
                    global_packages.insert(name, (version, "global".to_string()));
                }
            }
        }
    }
    
    if global_packages.is_empty() {
        ui::show_info("No global packages found");
        return Ok(());
    }
    
    if verbose {
        println!("{} Checking {} global packages for updates...", 
            "info".cyan().bold(),
            global_packages.len().to_string().bright_white().bold()
        );
    }
    
    // Check for outdated packages
    let outdated = check_outdated_packages(&global_packages, verbose).await?;
    
    if outdated.is_empty() {
        ui::show_success("All global packages are up to date! 🎉");
        return Ok(());
    }
    
    // Display outdated packages
    println!();
    println!("{}", "Global Packages:".bright_cyan().bold());
    display_outdated_table(&outdated, &global_packages);
    
    // Show update command for global packages
    show_global_update_suggestions(&outdated);
    
    Ok(())
}

async fn check_outdated_packages(
    packages: &HashMap<String, (String, String)>,
    verbose: bool,
) -> Result<Vec<UpdateInfo>> {
    let client = Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .context("Failed to create HTTP client")?;
    
    let mut outdated = Vec::new();
    
    let spinner = ui::create_resolution_spinner();
    spinner.set_message("Checking for package updates...");
    
    // Check packages concurrently (in batches to avoid rate limiting)
    let batch_size = 10;
    let package_list: Vec<_> = packages.iter().collect();
    
    for batch in package_list.chunks(batch_size) {
        let mut batch_futures = Vec::new();
        
        for (name, (version, _)) in batch {
            let client = client.clone();
            let name = (*name).clone();
            let version = (*version).clone();
            
            batch_futures.push(tokio::spawn(async move {
                check_package_outdated(&client, &name, &version).await
            }));
        }
        
        // Wait for batch to complete
        for future in batch_futures {
            if let Ok(Ok(Some(update_info))) = future.await {
                outdated.push(update_info);
            }
        }
    }
    
    spinner.finish_and_clear();
    
    // Sort by severity (major updates first, then by package name)
    outdated.sort_by(|a, b| {
        let severity_order = |update_type: &UpdateType| match update_type {
            UpdateType::Major => 0,
            UpdateType::Minor => 1,
            UpdateType::Patch => 2,
            UpdateType::Prerelease => 3,
        };
        
        severity_order(&a.update_type)
            .cmp(&severity_order(&b.update_type))
            .then(a.name.cmp(&b.name))
    });
    
    Ok(outdated)
}

async fn check_package_outdated(
    client: &Client,
    package_name: &str,
    current_version: &str,
) -> Result<Option<UpdateInfo>> {
    let url = format!("https://registry.npmjs.org/{}", urlencoding::encode(package_name));
    
    let response = client
        .get(&url)
        .header("User-Agent", "nx/0.2.0")
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Ok(None);
    }
    
    let manifest: Value = response.json().await?;
    
    let dist_tags = manifest.get("dist-tags").and_then(|t| t.as_object());
    let latest_version = dist_tags
        .and_then(|tags| tags.get("latest"))
        .and_then(|v| v.as_str())
        .unwrap_or("0.0.0");
    
    // Parse versions
    let current_clean = clean_version_spec(current_version);
    let current_ver = Version::parse(&current_clean).map_err(|e| anyhow::anyhow!("Failed to parse current version: {}", e))?;
    let latest_ver = Version::parse(latest_version).map_err(|e| anyhow::anyhow!("Failed to parse latest version: {}", e))?;
    
    // Also check for wanted version (satisfies current range)
    let wanted_version = find_wanted_version(&manifest, current_version).unwrap_or_else(|| latest_version.to_string());
    
    if latest_ver > current_ver {
        let update_type = determine_update_type(&current_ver, &latest_ver);
        
        Ok(Some(UpdateInfo {
            name: package_name.to_string(),
            current_version: current_ver.to_string(),
            latest_version: latest_ver.to_string(),
            wanted_version,
            update_type,
        }))
    } else {
        Ok(None)
    }
}

fn clean_version_spec(version_spec: &str) -> String {
    // Remove version prefixes like ^, ~, >=, etc.
    version_spec
        .trim_start_matches('^')
        .trim_start_matches('~')
        .trim_start_matches(">=")
        .trim_start_matches("<=")
        .trim_start_matches('>')
        .trim_start_matches('<')
        .trim_start_matches('=')
        .to_string()
}

fn find_wanted_version(manifest: &Value, version_spec: &str) -> Option<String> {
    // This is a simplified implementation
    // In reality, you'd need to parse the version range and find the highest version that satisfies it
    let versions = manifest.get("versions")?.as_object()?;
    
    // For now, just return the latest version if it starts with ^ or ~
    if version_spec.starts_with('^') || version_spec.starts_with('~') {
        let dist_tags = manifest.get("dist-tags")?.as_object()?;
        dist_tags.get("latest")?.as_str().map(|s| s.to_string())
    } else {
        None
    }
}

fn determine_update_type(current: &Version, latest: &Version) -> UpdateType {
    if latest.major > current.major {
        UpdateType::Major
    } else if latest.minor > current.minor {
        UpdateType::Minor
    } else if latest.patch > current.patch {
        UpdateType::Patch
    } else {
        UpdateType::Prerelease
    }
}

fn display_outdated_table(outdated: &[UpdateInfo], current_deps: &HashMap<String, (String, String)>) {
    println!();
    println!("{}", "📦 Outdated Packages".bright_red().bold());
    println!("{}", "─".repeat(80).dimmed());
    println!();
    
    println!("{:<25} {:<15} {:<15} {:<15} {}",
        "Package".bright_white().bold(),
        "Current".bright_blue().bold(),
        "Wanted".bright_yellow().bold(),
        "Latest".bright_green().bold(),
        "Type".bright_cyan().bold()
    );
    
    println!("{}", "─".repeat(80).dimmed());
    
    for update in outdated {
        let type_str = match update.update_type {
            UpdateType::Major => "major".bright_red().bold(),
            UpdateType::Minor => "minor".bright_yellow(),
            UpdateType::Patch => "patch".bright_green(),
            UpdateType::Prerelease => "prerelease".bright_magenta(),
        };
        
        let dep_type = current_deps.get(&update.name)
            .map(|(_, t)| match t.as_str() {
                "devDependency" => " (dev)".dimmed(),
                "optionalDependency" => " (opt)".dimmed(),
                _ => "".dimmed(),
            })
            .unwrap_or_else(|| "".dimmed());
        
        println!("{:<25} {:<15} {:<15} {:<15} {}{}",
            update.name.bright_white(),
            update.current_version.bright_blue(),
            update.wanted_version.bright_yellow(),
            update.latest_version.bright_green(),
            type_str,
            dep_type
        );
    }
    
    println!();
    
    // Summary
    let major_count = outdated.iter().filter(|u| matches!(u.update_type, UpdateType::Major)).count();
    let minor_count = outdated.iter().filter(|u| matches!(u.update_type, UpdateType::Minor)).count();
    let patch_count = outdated.iter().filter(|u| matches!(u.update_type, UpdateType::Patch)).count();
    
    print!("Summary: ");
    
    let mut parts = Vec::new();
    if major_count > 0 {
        parts.push(format!("{} major", major_count.to_string().bright_red().bold()));
    }
    if minor_count > 0 {
        parts.push(format!("{} minor", minor_count.to_string().bright_yellow()));
    }
    if patch_count > 0 {
        parts.push(format!("{} patch", patch_count.to_string().bright_green()));
    }
    
    println!("{}", parts.join(", "));
}

fn show_update_suggestions(outdated: &[UpdateInfo]) {
    println!();
    println!("{}", "💡 Update Commands".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    
    // Group by update type
    let major_updates: Vec<_> = outdated.iter().filter(|u| matches!(u.update_type, UpdateType::Major)).collect();
    let minor_patch_updates: Vec<_> = outdated.iter().filter(|u| !matches!(u.update_type, UpdateType::Major)).collect();
    
    if !minor_patch_updates.is_empty() {
        println!("  {} Safe updates (minor/patch):", "🟢".bright_green());
        println!("     {} {} {}", 
            "nx".bright_cyan(), 
            "update".bright_green(),
            minor_patch_updates.iter().map(|u| u.name.as_str()).collect::<Vec<_>>().join(" ").bright_white()
        );
        println!();
    }
    
    if !major_updates.is_empty() {
        println!("  {} Major updates (may contain breaking changes):", "🔴".bright_red());
        for update in major_updates {
            println!("     {} {} {}",
                "nx".bright_cyan(),
                "update".bright_green(), 
                update.name.bright_white()
            );
        }
        println!();
    }
    
    println!("  {} Update all packages:", "⚡".bright_yellow());
    println!("     {} {}", "nx".bright_cyan(), "update".bright_green());
    
    println!();
}

fn show_global_update_suggestions(outdated: &[UpdateInfo]) {
    println!();
    println!("{}", "💡 Global Update Commands".bright_cyan().bold());
    println!("{}", "─".repeat(40).dimmed());
    
    println!("  {} Update individual packages:", "📦".bright_blue());
    for update in outdated.iter().take(5) {
        println!("     {} {} {} {}",
            "nx".bright_cyan(),
            "install".bright_green(),
            "--global".bright_white(),
            format!("{}@{}", update.name, update.latest_version).bright_white()
        );
    }
    
    if outdated.len() > 5 {
        println!("     {} ... and {} more packages", 
            "...".dimmed(),
            (outdated.len() - 5).to_string().dimmed()
        );
    }
    
    println!();
    println!("  {} Update all global packages:", "⚡".bright_yellow());
    println!("     {} {} {}", "nx".bright_cyan(), "update".bright_green(), "--global".bright_white());
    
    println!();
}
